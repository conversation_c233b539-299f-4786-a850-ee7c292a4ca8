#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试替代MCP服务器的功能
"""

import requests
import json
import re

def test_zentao_html_access():
    """测试禅道HTML访问"""
    print("🔍 测试禅道HTML访问...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 登录
        login_url = f"{base_url}/index.php?m=user&f=login"
        login_data = {
            'account': 'admin',
            'password': 'ZLC#pkv5'
        }
        
        response = session.post(login_url, data=login_data, timeout=10)
        print(f"登录状态: {response.status_code}")
        
        if 'zentaosid' in session.cookies:
            print("✅ 登录成功")
            
            # 访问项目页面
            project_url = f"{base_url}/index.php?m=project&f=browse"
            response = session.get(project_url, timeout=10)
            print(f"项目页面状态: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"页面内容长度: {len(content)}")
                
                # 保存页面内容用于分析
                with open('project_page.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 页面内容已保存到 project_page.html")
                
                # 分析页面内容
                analyze_project_page(content)
                
                return True
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return False

def analyze_project_page(content):
    """分析项目页面内容"""
    print("\n📊 分析项目页面内容...")
    
    # 查找TBM相关内容
    tbm_patterns = [
        r'([^<>\n]*TBM[^<>\n]*)',
        r'([^<>\n]*智能建造[^<>\n]*)',
        r'([^<>\n]*建造系统[^<>\n]*)',
        r'([^<>\n]*二期[^<>\n]*)',
    ]
    
    tbm_found = []
    for pattern in tbm_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            match = match.strip()
            if match and len(match) > 5:
                tbm_found.append(match)
    
    if tbm_found:
        print(f"🎯 找到 {len(tbm_found)} 个TBM相关内容:")
        for item in tbm_found[:10]:  # 只显示前10个
            print(f"  - {item}")
    else:
        print("⚠️  未找到TBM相关内容")
    
    # 查找项目表格
    print(f"\n📋 查找项目表格...")
    
    # 查找表格行
    table_patterns = [
        r'<tr[^>]*>(.*?)</tr>',
        r'<tbody[^>]*>(.*?)</tbody>',
    ]
    
    projects_found = []
    for pattern in table_patterns:
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        print(f"找到 {len(matches)} 个表格匹配")
        
        for match in matches:
            # 在每个匹配中查找项目信息
            cell_pattern = r'<td[^>]*>(.*?)</td>'
            cells = re.findall(cell_pattern, match, re.DOTALL | re.IGNORECASE)
            
            if len(cells) >= 2:
                # 清理HTML标签
                clean_cells = []
                for cell in cells:
                    clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                    if clean_cell:
                        clean_cells.append(clean_cell)
                
                if clean_cells:
                    projects_found.append(clean_cells)
    
    if projects_found:
        print(f"📋 找到 {len(projects_found)} 个可能的项目记录:")
        for i, project in enumerate(projects_found[:10]):  # 只显示前10个
            print(f"  {i+1}. {' | '.join(project[:3])}")  # 只显示前3列
    
    # 查找链接
    print(f"\n🔗 查找项目链接...")
    link_pattern = r'<a[^>]*href="([^"]*)"[^>]*>([^<]+)</a>'
    links = re.findall(link_pattern, content, re.IGNORECASE)
    
    project_links = []
    for href, text in links:
        if 'project' in href.lower() and len(text.strip()) > 2:
            project_links.append((href, text.strip()))
    
    if project_links:
        print(f"🔗 找到 {len(project_links)} 个项目链接:")
        for href, text in project_links[:10]:  # 只显示前10个
            print(f"  - {text} ({href})")
    
    # 查找JavaScript数据
    print(f"\n📜 查找JavaScript数据...")
    js_patterns = [
        r'var\s+projects\s*=\s*(\[.*?\]);',
        r'projects\s*:\s*(\[.*?\])',
        r'data\s*:\s*(\[.*?\])',
    ]
    
    for pattern in js_patterns:
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        if matches:
            print(f"📜 找到JavaScript数据: {len(matches)} 个匹配")
            for match in matches:
                try:
                    # 尝试解析JSON
                    data = json.loads(match)
                    print(f"  ✅ 成功解析JSON，包含 {len(data)} 项")
                    if data and isinstance(data[0], dict):
                        print(f"  第一项: {data[0]}")
                except json.JSONDecodeError:
                    print(f"  ❌ JSON解析失败")

def main():
    """主函数"""
    print("========================================")
    print("测试替代MCP服务器功能")
    print("========================================")
    
    # 测试HTML访问
    if test_zentao_html_access():
        print(f"\n✅ HTML访问测试成功")
        print(f"💡 可以使用HTML解析方式获取项目信息")
    else:
        print(f"\n❌ HTML访问测试失败")

if __name__ == "__main__":
    main()
