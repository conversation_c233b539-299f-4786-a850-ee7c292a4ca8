#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试禅道连接问题的脚本
"""

import os
import sys
import requests
import json

def load_config():
    """加载配置文件"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        print("📝 从config.env加载配置...")
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    config[key] = value
                    print(f"  {key}: {'***' if 'PASSWORD' in key else value}")
    
    return config

def test_basic_connection(base_url):
    """测试基础连接"""
    print(f"\n🔍 测试基础连接: {base_url}")
    
    # 移除API路径，测试基础禅道页面
    zentao_base = base_url.replace('/api.php/v1', '')
    
    try:
        response = requests.get(zentao_base, timeout=10)
        print(f"  状态码: {response.status_code}")
        print(f"  响应长度: {len(response.text)} 字符")
        print(f"  Content-Type: {response.headers.get('Content-Type', 'unknown')}")
        
        # 检查是否是禅道页面
        if 'zentao' in response.text.lower() or 'login' in response.text.lower():
            print("  ✅ 看起来是禅道页面")
            return True
        else:
            print("  ⚠️  可能不是禅道页面")
            print(f"  前100个字符: {response.text[:100]}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 连接失败: {e}")
        return False
    
    return True

def test_api_endpoint(base_url):
    """测试API端点"""
    print(f"\n🔍 测试API端点: {base_url}")
    
    try:
        response = requests.get(base_url, timeout=10)
        print(f"  状态码: {response.status_code}")
        print(f"  响应长度: {len(response.text)} 字符")
        print(f"  Content-Type: {response.headers.get('Content-Type', 'unknown')}")
        print(f"  前200个字符: {response.text[:200]}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print("  ✅ 返回有效JSON")
                return True
            except json.JSONDecodeError:
                print("  ❌ 返回的不是有效JSON")
                return False
        else:
            print(f"  ❌ HTTP状态码错误: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 连接失败: {e}")
        return False

def test_token_request(base_url, username, password):
    """测试Token请求"""
    print(f"\n🔍 测试Token请求...")
    
    token_url = f"{base_url}/tokens"
    data = {
        'account': username,
        'password': password
    }
    
    print(f"  URL: {token_url}")
    print(f"  用户名: {username}")
    print(f"  密码: ***")
    
    try:
        response = requests.post(token_url, json=data, timeout=10)
        print(f"  状态码: {response.status_code}")
        print(f"  响应长度: {len(response.text)} 字符")
        print(f"  Content-Type: {response.headers.get('Content-Type', 'unknown')}")
        
        print(f"  完整响应: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print("  ✅ 返回有效JSON")
                if 'token' in json_data:
                    print(f"  ✅ Token获取成功: {json_data['token'][:20]}...")
                    return json_data['token']
                else:
                    print(f"  ❌ 响应中没有token字段: {json_data}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
        else:
            print(f"  ❌ HTTP状态码错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
    
    return None

def test_projects_api(base_url, token):
    """测试项目API"""
    print(f"\n🔍 测试项目API...")
    
    projects_url = f"{base_url}/projects"
    headers = {"Token": token}
    
    try:
        response = requests.get(projects_url, headers=headers, timeout=10)
        print(f"  状态码: {response.status_code}")
        print(f"  响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            try:
                projects = response.json()
                print(f"  ✅ 获取到 {len(projects)} 个项目")
                
                # 查找TBM相关项目
                tbm_projects = []
                print("  📋 项目列表:")
                for i, project in enumerate(projects[:10]):  # 只显示前10个
                    project_name = project.get('name', '')
                    project_id = project.get('id', '')
                    print(f"    {i+1}. {project_name} (ID: {project_id})")
                    
                    if 'TBM' in project_name or 'tbm' in project_name.lower() or '智能建造' in project_name:
                        tbm_projects.append(project)
                
                if tbm_projects:
                    print(f"  🎯 找到 {len(tbm_projects)} 个TBM相关项目:")
                    for project in tbm_projects:
                        print(f"    - {project.get('name')} (ID: {project.get('id')})")
                
                return projects, tbm_projects
                
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                print(f"  响应内容: {response.text[:500]}")
        else:
            print(f"  ❌ HTTP状态码错误: {response.status_code}")
            print(f"  响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
    
    return [], []

def main():
    """主函数"""
    print("========================================")
    print("禅道连接调试脚本")
    print("========================================")
    
    # 加载配置
    config = load_config()
    
    base_url = config.get('BASE_URL')
    username = config.get('USERNAME')
    password = config.get('PASSWORD')
    
    if not all([base_url, username, password]):
        print("❌ 配置不完整，请检查config.env文件")
        return
    
    print(f"\n🔧 使用配置:")
    print(f"  BASE_URL: {base_url}")
    print(f"  USERNAME: {username}")
    print(f"  PASSWORD: ***")
    
    # 测试基础连接
    if not test_basic_connection(base_url):
        print("\n❌ 基础连接失败，请检查禅道服务器是否运行")
        return
    
    # 测试API端点
    if not test_api_endpoint(base_url):
        print("\n❌ API端点测试失败")
    
    # 测试Token请求
    token = test_token_request(base_url, username, password)
    if not token:
        print("\n❌ Token获取失败，请检查用户名密码")
        return
    
    # 测试项目API
    projects, tbm_projects = test_projects_api(base_url, token)
    
    if tbm_projects:
        print(f"\n🎉 成功！找到 {len(tbm_projects)} 个TBM相关项目")
        print("\n📝 建议的下一步:")
        print("1. 配置看起来正确")
        print("2. 可以启动MCP服务器")
        print("3. 可以查询TBM项目执行情况")
    else:
        print(f"\n⚠️  连接成功但未找到TBM项目")
        print(f"总共有 {len(projects)} 个项目")

if __name__ == "__main__":
    main()
