# PowerShell脚本来查询TBM项目
# 设置环境变量并运行查询

Write-Host "========================================" -ForegroundColor Green
Write-Host "TBM智能建造系统(二期)项目查询" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 设置环境变量
$env:BASE_URL = "http://**********//zentao/api.php/v1"
$env:USERNAME = "admin"
$env:PASSWORD = "ZLC#pkv5"  # 请替换为实际密码
$env:LOG_LEVEL = "ERROR"

Write-Host "🔧 环境变量设置:" -ForegroundColor Yellow
Write-Host "  BASE_URL: $env:BASE_URL"
Write-Host "  USERNAME: $env:USERNAME"
Write-Host "  PASSWORD: ***"

Write-Host "`n🚀 开始查询..." -ForegroundColor Yellow

# 运行Python查询脚本
python query_with_env.py

Write-Host "`n✅ 查询完成!" -ForegroundColor Green
