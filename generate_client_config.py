#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP客户端配置生成器
为不同的MCP客户端生成配置文件
"""

import os
import sys
import json

def load_config():
    """加载配置文件"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    
    # 从环境变量加载配置
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if key in os.environ:
            config[key] = os.environ[key]
    
    return config

def generate_claude_desktop_config():
    """生成Claude Desktop配置"""
    config = load_config()
    current_dir = os.path.abspath('.')
    python_path = sys.executable
    main_path = os.path.join(current_dir, 'main.py')
    
    claude_config = {
        "mcpServers": {
            "mcp-server-zentao": {
                "command": python_path,
                "args": [main_path],
                "env": {}
            }
        }
    }
    
    # 添加环境变量
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if config.get(key):
            claude_config["mcpServers"]["mcp-server-zentao"]["env"][key] = config[key]
    
    return claude_config

def generate_cline_config():
    """生成Cline配置"""
    config = load_config()
    current_dir = os.path.abspath('.')
    python_path = sys.executable
    main_path = os.path.join(current_dir, 'main.py')
    
    cline_config = {
        "mcp-server-zentao": {
            "disabled": False,
            "timeout": 60,
            "command": python_path,
            "args": [main_path],
            "env": {},
            "transportType": "stdio",
            "autoApprove": []
        }
    }
    
    # 添加环境变量
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if config.get(key):
            cline_config["mcp-server-zentao"]["env"][key] = config[key]
    
    return cline_config

def generate_continue_config():
    """生成Continue配置"""
    config = load_config()
    current_dir = os.path.abspath('.')
    python_path = sys.executable
    main_path = os.path.join(current_dir, 'main.py')
    
    continue_config = {
        "mcpServers": [
            {
                "name": "zentao",
                "command": python_path,
                "args": [main_path],
                "env": {}
            }
        ]
    }
    
    # 添加环境变量
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if config.get(key):
            continue_config["mcpServers"][0]["env"][key] = config[key]
    
    return continue_config

def main():
    """主函数"""
    print("========================================")
    print("MCP客户端配置生成器")
    print("========================================")
    
    config = load_config()
    if not config:
        print("❌ 未找到配置文件，请先运行 setup_config.bat 或创建 config.env")
        return
    
    print("📝 生成配置文件...")
    
    # 生成Claude Desktop配置
    claude_config = generate_claude_desktop_config()
    with open('claude_desktop_config.json', 'w', encoding='utf-8') as f:
        json.dump(claude_config, f, indent=2, ensure_ascii=False)
    print("✅ Claude Desktop配置: claude_desktop_config.json")
    
    # 生成Cline配置
    cline_config = generate_cline_config()
    with open('cline_config.json', 'w', encoding='utf-8') as f:
        json.dump(cline_config, f, indent=2, ensure_ascii=False)
    print("✅ Cline配置: cline_config.json")
    
    # 生成Continue配置
    continue_config = generate_continue_config()
    with open('continue_config.json', 'w', encoding='utf-8') as f:
        json.dump(continue_config, f, indent=2, ensure_ascii=False)
    print("✅ Continue配置: continue_config.json")
    
    print("\n📋 配置使用说明:")
    print("1. Claude Desktop: 将claude_desktop_config.json内容复制到Claude Desktop的配置文件中")
    print("2. Cline: 将cline_config.json内容复制到Cline的MCP服务器配置中")
    print("3. Continue: 将continue_config.json内容复制到Continue的配置文件中")
    
    print(f"\n🔧 当前配置摘要:")
    print(f"   - 基础URL: {config.get('BASE_URL', '未配置')}")
    print(f"   - 用户名: {config.get('USERNAME', '未配置')}")
    print(f"   - TOKEN: {'已配置' if config.get('TOKEN') else '未配置'}")
    print(f"   - Python路径: {sys.executable}")
    print(f"   - 脚本路径: {os.path.abspath('main.py')}")

if __name__ == "__main__":
    main()
