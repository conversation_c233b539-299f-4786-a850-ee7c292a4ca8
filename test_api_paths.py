#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的禅道API路径
"""

import requests
import json

def test_api_paths():
    """测试不同的API路径"""
    print("测试不同的禅道API路径...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 可能的API路径
    api_paths = [
        "/api.php/v1",
        "/api/v1", 
        "/index.php?m=api&f=getModel",
        "/index.php?m=api",
        "/api",
        "/rest/v1",
        "/api.php",
        "/www/api.php/v1"
    ]
    
    for path in api_paths:
        full_url = base_url + path
        print(f"\n测试: {full_url}")
        
        try:
            response = requests.get(full_url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:200]
                print(f"  内容预览: {content}")
                
                # 检查是否是JSON
                try:
                    json_data = json.loads(response.text)
                    print("  ✅ 返回有效JSON")
                    print(f"  JSON内容: {json_data}")
                except json.JSONDecodeError:
                    if 'html' in content.lower():
                        print("  ❌ 返回HTML页面")
                    else:
                        print("  ❌ 不是有效JSON")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")

def test_token_endpoints():
    """测试Token获取端点"""
    print("\n\n测试Token获取端点...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 可能的Token端点
    token_paths = [
        "/api.php/v1/tokens",
        "/api/v1/tokens",
        "/index.php?m=api&f=getSessionID",
        "/index.php?m=user&f=login",
        "/api/tokens",
        "/rest/v1/tokens",
        "/api.php/tokens"
    ]
    
    credentials = {"account": "admin", "password": "ZLC#pkv5"}
    
    for path in token_paths:
        full_url = base_url + path
        print(f"\n测试Token: {full_url}")
        
        try:
            # 尝试POST请求
            response = requests.post(full_url, json=credentials, timeout=5)
            print(f"  POST状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"  响应长度: {len(content)}")
                
                try:
                    json_data = json.loads(content)
                    print("  ✅ 返回有效JSON")
                    if 'token' in json_data:
                        print(f"  🎉 找到Token: {json_data['token'][:20]}...")
                        return full_url, json_data['token']
                    else:
                        print(f"  JSON内容: {json_data}")
                except json.JSONDecodeError:
                    if len(content) < 500:
                        print(f"  响应内容: {content}")
                    else:
                        print(f"  响应预览: {content[:200]}...")
            
            # 如果POST失败，尝试GET
            if response.status_code != 200:
                response = requests.get(full_url, timeout=5)
                print(f"  GET状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"  GET响应: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
    
    return None, None

def test_alternative_auth():
    """测试其他认证方式"""
    print("\n\n测试其他认证方式...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 尝试基础认证
    try:
        print("测试基础认证...")
        auth_url = base_url + "/index.php?m=user&f=login"
        
        # 尝试表单数据
        form_data = {
            'account': 'admin',
            'password': 'ZLC#pkv5'
        }
        
        response = requests.post(auth_url, data=form_data, timeout=5)
        print(f"表单认证状态码: {response.status_code}")
        
        if 'Set-Cookie' in response.headers:
            print("✅ 获得Cookie")
            cookies = response.cookies
            
            # 尝试使用Cookie访问API
            api_url = base_url + "/index.php?m=project&f=browse"
            response2 = requests.get(api_url, cookies=cookies, timeout=5)
            print(f"Cookie访问状态码: {response2.status_code}")
            
    except Exception as e:
        print(f"❌ 基础认证失败: {e}")

def main():
    """主函数"""
    print("========================================")
    print("禅道API路径测试")
    print("========================================")
    
    # 测试API路径
    test_api_paths()
    
    # 测试Token端点
    token_url, token = test_token_endpoints()
    
    if token:
        print(f"\n🎉 成功获取Token!")
        print(f"正确的Token URL: {token_url}")
        print(f"Token: {token[:20]}...")
        
        # 更新配置建议
        api_base = token_url.replace('/tokens', '')
        print(f"\n📝 建议的配置:")
        print(f"BASE_URL={api_base}")
    else:
        print(f"\n❌ 未能获取Token，尝试其他认证方式...")
        test_alternative_auth()

if __name__ == "__main__":
    main()
