"""
用户相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

USER_URL = BASE_URL + '/users'
OPERATE_USER_URL = BASE_URL + '/users/{}'


def list_all():
    return req(Method.GET, USER_URL, headers=HEADER)


def create(account, password, real_name, gender):
    data = {
        'account': account,
        'password': password,
        'realname': real_name,
        'gender': gender
    }
    return req(Method.POST, USER_URL, headers=HEADER, body=data)


def get(user_id):
    return req(Method.GET, OPERATE_USER_URL.format(user_id), headers=HEADER)


def delete(user_id):
    return req(Method.DELETE, OPERATE_USER_URL.format(user_id), headers=HEADER)


def update(user_id, key, value):
    data = dict()
    data[key] = value
    return req(Method.PUT, OPERATE_USER_URL.format(user_id), headers=HEADER, body=data)
