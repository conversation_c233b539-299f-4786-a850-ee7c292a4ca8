from mcp.server.fastmcp import FastMCP

from service import project_service, product_service, execution_service, task_service, user_service, story_service, \
    bug_service

mcp = FastMCP("ZenTao", log_level="ERROR")


# ============= MCP Server 信息 =============
@mcp.resource("config://version")
def get_version():
    """
    获取Mcp Server版本信息
    """
    return "ZenTao Mcp Server Version: 0.0.3"


@mcp.resource("config://dict")
def list_dict():
    """
    获取Mcp Server字典信息
    """
    return [
        {
            "name": "需求类型",
            "list": [
                {
                    "key": "功能",
                    "value": "feature"
                },
                {
                    "key": "接口",
                    "value": "interface"
                },
                {
                    "key": "安全",
                    "value": "safe"
                },
                {
                    "key": "体验",
                    "value": "experience"
                },
                {
                    "key": "改进",
                    "value": "improve"
                },
                {
                    "key": "其他",
                    "value": "other"
                },
            ]
        }
    ]


# ============= 用户相关 =============
@mcp.tool("获取用户列表")
def list_user():
    """
    获取所有用户列表
    """
    return user_service.list_all()


@mcp.tool("创建用户")
def create_user(account: str, password: str, real_name: str, gender: str):
    """
    根据用户名和密码创建用户
    :param account 用户名
    :param password 密码
    :param real_name 用户真实姓名
    :param gender: 性别: f 女性, m 男性
    """
    return user_service.create(account, password, real_name, gender)


@mcp.tool("查询用户详情")
def query_user(user_id: int):
    """
    根据用户ID获取用户详细信息
    :param user_id 用户ID
    """
    return user_service.get(user_id)


@mcp.tool("删除用户")
def delete_user(user_id: int):
    """
    根据用户ID删除用户
    :param user_id 用户ID
    """
    return user_service.delete(user_id)


@mcp.tool("修改用户")
def delete_user(user_id: int, key: str, value: str):
    """
    根据用户ID修改用户信息
    :param user_id 用户ID
    :param key 修改的用户信息：包括 dept(部门ID)、role(角色)、mobile(手机号)、realname(真实姓名)、email(邮箱)、phone(电话号码)
    :param value 修改值
    """
    return user_service.update(user_id, key, value)


# ============= 产品相关 =============
@mcp.tool("获取产品列表")
def list_product():
    """
    获取所有产品列表
    """
    return product_service.list_all()


@mcp.tool("创建产品")
def create_product(name: str, code: str):
    """
    根据产品名称和产品代码创建一个产品
    :param name 产品名称
    :param code 产品代码
    """
    return product_service.create(name, code)


@mcp.tool("查询产品详情")
def query_product(product_id: int):
    """
    根据产品ID获取产品详细信息
    :param product_id 产品ID
    """
    return product_service.get(product_id)


@mcp.tool("删除产品")
def delete_product(product_id: int):
    """
    根据产品ID删除产品
    :param product_id 产品ID
    """
    return product_service.delete(product_id)


# ============= 项目相关 =============
@mcp.tool("获取项目列表")
def list_project():
    """
    获取所有项目列表
    """
    return project_service.list_all()


@mcp.tool("创建项目")
def create_project(name: str, code: str, begin_time: str, end_time: str, product_id: int):
    """
    根据项目名称、项目代码、计算开始结束时间和产品ID创建项目
    :param name 项目名称
    :param code 项目代码
    :param begin_time: 计划开始时间
    :param end_time: 计划结束时间
    :param product_id: 关联产品ID
    """
    return project_service.create(name, code, begin_time, end_time, product_id)


@mcp.tool("查询项目详情")
def query_project(project_id: int):
    """
    根据项目ID查询项目详细信息
    :param project_id 项目ID
    """
    return project_service.get(project_id)


@mcp.tool("删除项目")
def delete_project(project_id: int):
    """
    根据项目ID删除项目
    :param project_id 项目ID
    """
    return project_service.delete(project_id)


# ============= 项目执行相关 =============
@mcp.tool("获取项目执行列表")
def list_execution(project_id: int):
    """
    获取项目的执行列表
    :param project_id 项目ID
    """
    return execution_service.list_all(project_id)


@mcp.tool("创建项目执行")
def create_execution(name: str, code: str, begin_time: str, end_time: str, project_id: int):
    """
    根据项目执行名称、代码、计划开始结束时间和项目ID创建项目执行
    :param name 执行名称
    :param code 执行代码
    :param begin_time: 计划开始时间
    :param end_time: 计划结束时间
    :param project_id 项目ID
    """
    return execution_service.create(name, code, begin_time, end_time, project_id)


@mcp.tool("查询项目执行详情")
def query_execution(execution_id: int):
    """
    根据项目执行ID查询项目执行详细信息
    :param execution_id 项目执行ID
    """
    return execution_service.get(execution_id)


@mcp.tool("删除项目执行")
def delete_execution(execution_id: int):
    """
    根据执行ID删除执行
    :param execution_id 项目执行ID
    """
    return execution_service.delete(execution_id)


# ============= 任务相关 =============
@mcp.tool("获取任务列表")
def list_task(execution_id: int):
    """
    获取项目执行的任务列表
    :param execution_id 项目执行ID
    """
    return task_service.list_all(execution_id)


@mcp.tool("创建任务")
def create_task(name: str, assigned_to: str, est_started: str, deadline: str, type: str, execution_id: int):
    """
    创建任务
    :param name 任务名称
    :param assigned_to 指派用户
    :param est_started 计划开始时间
    :param deadline 计划结束时间
    :param type 任务类型(design 设计 | devel 开发 | request 需求 | test 测试 | study 研究 | discuss 讨论 | ui 界面 | affair 事务 | misc 其他)
    :param execution_id 项目执行ID
    """
    return task_service.create(name, assigned_to, est_started, deadline, type, execution_id)


@mcp.tool("查询任务详情")
def query_task(task_id: int):
    """
    根据任务ID查询任务详细信息
    :param task_id 任务ID
    """
    return task_service.get(task_id)


@mcp.tool("删除任务")
def delete_task(task_id: int):
    """
    根据任务ID删除任务
    :param task_id 任务ID
    """
    return task_service.delete(task_id)


@mcp.tool("修改任务")
def update_task(task_id: int, key: str, value: str):
    """
    根据任务ID修改任务信息
    :param task_id 任务ID
    :param key 修改的任务信息：包括 name(任务名称)、type(任务类型)、assignedTo(指派用户名)、pri(优先级)、estimate(预计工时)、estStarted(预计开始时间)、deadline(预计结束时间)
    :param value 修改值
    """
    return task_service.update(task_id, key, value)


@mcp.prompt("分析项目任务", description="进行任务分析的提示词")
def analysis_task(task_id: int):
    """
    根据任务信息对任务进行分析
    :param task_id 任务ID
    """
    task_info = task_service.get(task_id)
    return """
    ## 角色
    你是一个禅道任务分析大师，能够对任务数据进行全面的分析，找出风险点并给出优化建议
    **数据**
    %s
    """ % task_info


# ============= 需求相关 =============
@mcp.tool("获取需求列表")
def list_product_story(type: str, entity_id: int):
    """
    获取需求列表
    :param type 类型(product 产品 | project 项目 | execution 执行)
    :param entity_id 对应实体ID
    """
    if type == "product":
        return story_service.list_product_all(entity_id)
    elif type == "project":
        return story_service.list_project_all(entity_id)
    else:
        return story_service.list_execution_all(entity_id)


@mcp.tool("创建需求")
def create_story(title: str, spec: str, pri: str, category: str, reviewer: list, product_id: int):
    """
    创建需求
    :param title 需求名称
    :param spec 需求描述
    :param pri 优先级(1 | 2 | 3 | 4)
    :param category 需求类型(feature 功能 | interface 接口 | performance 性能 | safe 安全 | experience 体验 | improve 改进 | other 其他)
    :param reviewer 评审人员列表
    :param product_id 产品ID
    """
    return story_service.create(title, spec, product_id, pri, category, reviewer)


@mcp.tool("查询需求详情")
def query_story(story_id: int):
    """
    根据需求ID查询需求详细信息
    :param story_id 需求ID
    """
    return story_service.get(story_id)


@mcp.tool("删除需求")
def delete_story(story_id: int):
    """
    根据需求ID删除需求
    :param story_id 需求ID
    """
    return story_service.delete(story_id)


@mcp.tool("修改需求")
def update_story(story_id: int, key: str, value: str, reviewer: list):
    """
    根据需求ID修改需求信息
    :param story_id 需求ID
    :param key 修改的需求信息：包括 title(需求名称)、spec(需求描述)、verify(验收标准)、pri(优先级)、category(需求类型)、estimate(预计工时)、keywords(关键词)
    :param value 修改值
    :param reviewer 评审人员列表
    """
    return story_service.update(story_id, key, value, reviewer)


# ============= BUG相关 =============
@mcp.tool("获取BUG列表")
def list_bug(product_id: int):
    """
    获取产品的BUG列表
    :param product_id 产品ID
    """
    return bug_service.list_all(product_id)


@mcp.tool("创建BUG")
def create_bug(title: str, severity: int, pri: int, type: str, product_id: int):
    """
    创建bug
    :param title bug标题
    :param severity 严重程度
    :param pri 优先级
    :param type bug类型(codeerror 代码错误 | config 配置相关 | install 安装部署 | security 安全相关 | performance 性能问题 | standard 标准规范 | automation |测试脚本 | designdefect 设计缺陷 | others 其他)
    :param product_id 产品ID
    """
    return bug_service.create(title, severity, pri, type, product_id)


@mcp.tool("查询BUG详情")
def query_bug(bug_id: int):
    """
    根据bugID查询bug详细信息
    :param bug_id bugID
    """
    return bug_service.get(bug_id)


@mcp.tool("删除BUG")
def delete_bug(bug_id: int):
    """
    根据bugID删除bug
    :param bug_id bugID
    """
    return bug_service.delete(bug_id)


@mcp.tool("修改BUG")
def update_bug(bug_id: int, key: str, value: str):
    """
    根据bugID修改bug信息
    :param bug_id bugID
    :param key 修改的bug信息：包括 title(标题)、severity(严重程度)、steps(重现步骤)、pri(优先级)、type(bug类型)
    :param value 修改值
    """
    return bug_service.update(bug_id, key, value)


if __name__ == "__main__":
    mcp.run(transport='stdio')  # 启用调试模式
