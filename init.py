import os

from config.config import BASE_URL
from service.token_service import get_token

# 解析环境变量
ENV_BASE_URL = os.getenv("BASE_URL")
USERNAME = os.getenv("USERNAME")
PASSWORD = os.getenv("PASSWORD")
ENV_TOKEN = os.getenv("TOKEN")
TOKEN = None

if BASE_URL is not None:
    BASE_URL = ENV_BASE_URL
if ENV_TOKEN is None:
    if USERNAME is None:
        raise ValueError("USERNAME environment variable required")
    if PASSWORD is None:
        raise ValueError("PASSWORD environment variable required")
    TOKEN = get_token(USERNAME, PASSWORD)
else:
    TOKEN = ENV_TOKEN

# 请求头
HEADER = {
    "Token": TOKEN
}
