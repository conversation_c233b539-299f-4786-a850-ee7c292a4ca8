#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禅道MCP服务器测试脚本
用于测试MCP服务器的各项功能是否正常工作
"""

import os
import sys
import json
import subprocess
import time
from typing import Dict, Any

def load_config():
    """加载配置文件"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    
    # 从环境变量加载配置
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if key in os.environ:
            config[key] = os.environ[key]
    
    return config

def test_import():
    """测试导入依赖"""
    print("🔍 测试导入依赖...")
    try:
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP 导入成功")
        return True
    except ImportError as e:
        print(f"❌ FastMCP 导入失败: {e}")
        print("💡 请安装依赖: pip install mcp")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试配置...")
    config = load_config()
    
    if not config.get('BASE_URL'):
        print("❌ 未配置 BASE_URL")
        return False
    
    if not (config.get('USERNAME') and config.get('PASSWORD')) and not config.get('TOKEN'):
        print("❌ 未配置认证信息（用户名密码或TOKEN）")
        return False
    
    print(f"✅ 基础URL: {config['BASE_URL']}")
    if config.get('USERNAME'):
        print(f"✅ 用户名: {config['USERNAME']}")
    if config.get('TOKEN'):
        print("✅ TOKEN已配置")
    
    return True

def test_server_start():
    """测试服务器启动"""
    print("\n🔍 测试服务器启动...")
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, 'main.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间
        time.sleep(2)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务器启动成功")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 服务器启动失败")
            if stderr:
                print(f"错误信息: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 服务器启动测试失败: {e}")
        return False

def test_zentao_connection():
    """测试禅道连接"""
    print("\n🔍 测试禅道连接...")
    try:
        config = load_config()
        base_url = config.get('BASE_URL')
        
        if not base_url:
            print("❌ 未配置BASE_URL")
            return False
        
        import requests
        
        # 测试基础连接
        try:
            response = requests.get(base_url.replace('/api.php/v1', ''), timeout=5)
            print(f"✅ 禅道服务器连接成功 (状态码: {response.status_code})")
            return True
        except requests.exceptions.RequestException as e:
            print(f"❌ 禅道服务器连接失败: {e}")
            print("💡 请检查禅道服务器是否正在运行，以及BASE_URL是否正确")
            return False
            
    except ImportError:
        print("⚠️  未安装requests库，跳过连接测试")
        print("💡 可以安装: pip install requests")
        return True

def generate_mcp_client_config():
    """生成MCP客户端配置"""
    print("\n📝 生成MCP客户端配置...")
    
    config = load_config()
    current_dir = os.path.abspath('.')
    python_path = sys.executable
    main_path = os.path.join(current_dir, 'main.py')
    
    mcp_config = {
        "mcp-server-zentao": {
            "disabled": False,
            "timeout": 60,
            "command": python_path,
            "args": [main_path],
            "env": {},
            "transportType": "stdio",
            "autoApprove": []
        }
    }
    
    # 添加环境变量
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if config.get(key):
            mcp_config["mcp-server-zentao"]["env"][key] = config[key]
    
    # 保存配置文件
    with open('mcp_client_config.json', 'w', encoding='utf-8') as f:
        json.dump(mcp_config, f, indent=2, ensure_ascii=False)
    
    print("✅ MCP客户端配置已生成: mcp_client_config.json")
    print("\n📋 配置内容:")
    print(json.dumps(mcp_config, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    print("========================================")
    print("禅道MCP服务器测试脚本")
    print("========================================")
    
    tests = [
        ("导入依赖", test_import),
        ("配置检查", test_config),
        ("服务器启动", test_server_start),
        ("禅道连接", test_zentao_connection),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            results.append((name, False))
    
    # 生成客户端配置
    generate_mcp_client_config()
    
    # 总结
    print("\n" + "="*40)
    print("测试结果总结")
    print("="*40)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！MCP服务器已准备就绪")
        print("💡 下一步：在MCP客户端中使用生成的配置文件")
    else:
        print("\n⚠️  部分测试失败，请检查配置和环境")

if __name__ == "__main__":
    main()
