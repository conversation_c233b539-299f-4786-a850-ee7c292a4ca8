"""
产品相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

PRODUCT_URL = BASE_URL + '/products'
OPERATE_PRODUCT_URL = BASE_URL + '/products/{}'


def list_all():
    return req(Method.GET, PRODUCT_URL, headers=HEADER)


def create(name, code):
    data = {
        'name': name,
        'code': code
    }
    return req(Method.POST, PRODUCT_URL, headers=HEADER, body=data)


def get(product_id):
    return req(Method.GET, OPERATE_PRODUCT_URL.format(product_id), headers=HEADER)


def delete(product_id):
    return req(Method.DELETE, OPERATE_PRODUCT_URL.format(product_id), headers=HEADER)
