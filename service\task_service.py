"""
任务相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

EXECUTIONS_TASKS_URL = BASE_URL + '/executions/{}/tasks'
TASKS_URL = BASE_URL + '/tasks/{}'


def list_all(execution_id):
    return req(Method.GET, EXECUTIONS_TASKS_URL.format(execution_id) + "?limit=1000", headers=HEADER)


def create(name, assigned_to, est_started, deadline, type, execution_id):
    data = {
        'name': name,
        'assignedTo': assigned_to,
        'estStarted': est_started,
        'deadline': deadline,
        'type': type
    }
    return req(Method.POST, EXECUTIONS_TASKS_URL.format(execution_id), headers=HEADER, body=data)


def get(task_id):
    return req(Method.GET, TASKS_URL.format(task_id), headers=HEADER)


def delete(task_id):
    return req(Method.DELETE, TASKS_URL.format(task_id), headers=HEADER)


def update(task_id, key, value):
    data = dict()
    data[key] = value
    return req(Method.PUT, TASKS_URL.format(task_id), headers=HEADER, body=data)
