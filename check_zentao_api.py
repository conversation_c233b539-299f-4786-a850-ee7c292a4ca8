#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查禅道API是否启用
"""

import requests

def check_zentao_api():
    """检查禅道API状态"""
    print("检查禅道API状态...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 检查主页面
    try:
        response = requests.get(base_url, timeout=5)
        print(f"主页状态: {response.status_code}")
        
        # 检查是否有API相关信息
        content = response.text.lower()
        if 'api' in content:
            print("✅ 页面中包含API相关内容")
        else:
            print("⚠️  页面中未发现API相关内容")
            
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
        return
    
    # 检查常见的API路径
    api_tests = [
        ("/api.php", "标准API入口"),
        ("/api", "简化API路径"),
        ("/index.php?m=api", "模块化API"),
        ("/rest", "REST API"),
    ]
    
    for path, desc in api_tests:
        url = base_url + path
        try:
            response = requests.get(url, timeout=3)
            print(f"{desc} ({path}): {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:100]
                if 'error' not in content.lower() and 'illegal' not in content.lower():
                    print(f"  ✅ 可能可用: {content}")
                else:
                    print(f"  ❌ 有错误: {content}")
            
        except Exception as e:
            print(f"{desc} ({path}): 连接失败")

def main():
    check_zentao_api()

if __name__ == "__main__":
    main()
