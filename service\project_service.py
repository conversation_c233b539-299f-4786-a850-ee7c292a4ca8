"""
项目相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

PROJECT_URL = BASE_URL + '/projects'
OPERATE_PROJECT_URL = BASE_URL + '/projects/{}'


def list_all():
    return req(Method.GET, PROJECT_URL + "?limit=1000", headers=HEADER)


def create(name, code, begin_time, end_time, product_id):
    data = {
        'name': name,
        'code': code,
        'begin': begin_time,
        'end': end_time,
        'products': [product_id]
    }
    return req(Method.POST, PROJECT_URL, headers=HEADER, body=data)


def get(project_id):
    return req(Method.GET, OPERATE_PROJECT_URL.format(project_id), headers=HEADER)


def delete(project_id):
    return req(Method.DELETE, OPERATE_PROJECT_URL.format(project_id), headers=HEADER)
