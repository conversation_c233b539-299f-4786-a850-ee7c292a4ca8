#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试禅道连接
"""

import requests

def quick_test():
    """快速测试"""
    print("快速测试禅道连接...")
    
    base_url = "http://10.95.2.86/zentao"
    api_url = "http://10.95.2.86/zentao/api.php/v1"
    token_url = "http://10.95.2.86/zentao/api.php/v1/tokens"
    
    # 测试基础页面
    try:
        print(f"测试: {base_url}")
        response = requests.get(base_url, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        if response.status_code == 200:
            print("✅ 基础页面可访问")
        else:
            print(f"❌ 状态码错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 基础页面测试失败: {e}")
    
    # 测试API端点
    try:
        print(f"\n测试: {api_url}")
        response = requests.get(api_url, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"内容: {response.text[:100]}")
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
    
    # 测试Token请求
    try:
        print(f"\n测试Token: {token_url}")
        data = {"account": "admin", "password": "ZLC#pkv5"}
        response = requests.post(token_url, json=data, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            import json
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"✅ Token获取成功: {result['token'][:20]}...")
                else:
                    print(f"❌ 没有token字段: {result}")
            except json.JSONDecodeError:
                print("❌ 不是有效JSON")
        
    except Exception as e:
        print(f"❌ Token测试失败: {e}")

if __name__ == "__main__":
    quick_test()
