import json

import requests


class Method:
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"


def req(method, url, headers=None, body=None):
    response = requests.request(method=method, url=url, headers=headers, json=body)

    # 检查HTTP状态码
    if response.status_code != 200:
        raise Exception(f"HTTP {response.status_code}: {response.text}")

    # 检查响应是否为空
    if not response.text.strip():
        raise Exception("Empty response from server")

    # 尝试解析JSON
    try:
        parse_res = json.loads(response.text)
    except json.JSONDecodeError as e:
        raise Exception(f"Invalid JSON response: {e}. Response content: {response.text[:200]}")

    # 检查业务错误
    if "error" in parse_res:
        raise Exception(parse_res['error'])

    return parse_res

