# 禅道MCP服务器问题解决方案

## 📋 问题总结

经过全面诊断，发现禅道API存在配置问题：
- 错误信息：`control file module/zentao/control.php not found`
- 原因：禅道的模块路径配置不正确
- 影响：无法使用标准的禅道API接口

## 🛠️ 解决方案

### 方案一：使用替代MCP服务器（推荐）

我已创建了一个替代的MCP服务器，通过HTML解析方式绕过API问题：

```bash
# 启动替代MCP服务器
python alternative_mcp_server.py
```

**功能特点：**
- ✅ 绕过API配置问题
- ✅ 支持项目查询
- ✅ 支持TBM项目查找
- ✅ 使用会话登录，稳定可靠

### 方案二：修复原始API问题

如果要修复原始API问题，需要：

1. **检查禅道安装**
   - 确认 `/apps/zentao/module/` 目录结构
   - 检查文件权限
   - 验证配置文件

2. **重新配置禅道**
   - 可能需要重新安装或修复禅道
   - 检查Apache/Nginx配置

## 🚀 快速使用

### 启动替代服务器

```bash
# 1. 确保配置正确
cat config.env

# 2. 启动替代MCP服务器
python alternative_mcp_server.py
```

### 测试功能

```bash
# 测试连接
python test_alternative_server.py

# 查看项目页面（会生成project_page.html）
# 可以手动分析HTML内容
```

## 📁 文件说明

### 诊断工具
- `debug_connection.py` - 全面连接诊断
- `quick_test.py` - 快速连接测试
- `check_zentao_version.py` - 版本检查
- `comprehensive_api_test.py` - API测试

### 解决方案
- `alternative_mcp_server.py` - 替代MCP服务器（主要解决方案）
- `fix_zentao_access.py` - HTML解析访问
- `verify_and_query.py` - API验证和查询

### 配置文件
- `config.env` - 已修复的配置文件
- `config.env.template` - 配置模板

## 🎯 查询TBM项目

使用替代MCP服务器后，可以通过以下工具查询：

```python
# 在MCP客户端中调用
list_projects()  # 获取所有项目
find_tbm_projects()  # 查找TBM相关项目
get_project_detail("项目ID")  # 获取项目详情
```

## 📊 当前状态

- ✅ 禅道服务器正常运行
- ✅ 网络连接正常
- ✅ 登录功能正常
- ❌ 原始API配置有问题
- ✅ 替代方案已就绪

## 💡 建议

1. **立即可用**：使用 `alternative_mcp_server.py`
2. **长期方案**：修复禅道API配置
3. **测试验证**：运行诊断工具确认功能

## 🔧 故障排除

如果替代服务器也有问题：

1. 检查网络连接：`ping 10.95.2.86`
2. 检查禅道访问：浏览器打开 `http://10.95.2.86/zentao`
3. 检查登录信息：确认用户名密码正确
4. 查看错误日志：检查Python错误输出

## 📞 技术支持

所有诊断工具和解决方案已创建完成，可以：
- 使用替代MCP服务器立即开始工作
- 根据诊断结果修复原始API问题
- 通过HTML解析方式查询TBM项目信息

---

**推荐操作顺序：**
1. 运行 `python alternative_mcp_server.py` 启动替代服务器
2. 在MCP客户端中配置连接
3. 使用 `find_tbm_projects()` 查询TBM智能建造系统(二期)项目
4. 根据需要获取项目详细信息
