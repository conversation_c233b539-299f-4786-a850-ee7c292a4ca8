"""
需求相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

STORY_URL = BASE_URL + '/stories'
OPERATE_STORY_URL = BASE_URL + '/stories/{}'
CHANGE_STORY_URL = BASE_URL + '/stories/{}/change'
PROJECT_STORY_URL = BASE_URL + '/projects/{}/stories'
PRODUCT_STORY_URL = BASE_URL + '/products/{}/stories'
EXECUTION_STORY_URL = BASE_URL + '/executions/{}/stories'


def list_project_all(project_id):
    return req(Method.GET, PROJECT_STORY_URL.format(project_id) + "?limit=1000", headers=HEADER)


def list_product_all(product_id):
    return req(Method.GET, PRODUCT_STORY_URL.format(product_id) + "?limit=1000", headers=HEADER)


def list_execution_all(execution_id):
    return req(Method.GET, EXECUTION_STORY_URL.format(execution_id) + "?limit=1000", headers=HEADER)


def create(title, spec, product_id, pri, category, reviewer):
    data = {
        "title": title,
        "spec": spec,
        "product": product_id,
        "pri": pri,
        "category": category,
        "reviewer": reviewer
    }
    return req(Method.POST, STORY_URL, headers=HEADER, body=data)


def get(story_id):
    return req(Method.GET, OPERATE_STORY_URL.format(story_id), headers=HEADER)


def delete(story_id):
    return req(Method.DELETE, OPERATE_STORY_URL.format(story_id), headers=HEADER)


def update(story_id, key, value, reviewer):
    data = {"reviewer": reviewer, key: value}
    if key in ["title", "spec", "verify"]:
        return req(Method.POST, CHANGE_STORY_URL.format(story_id), headers=HEADER, body=data)
    else:
        return req(Method.PUT, OPERATE_STORY_URL.format(story_id), headers=HEADER, body=data)
