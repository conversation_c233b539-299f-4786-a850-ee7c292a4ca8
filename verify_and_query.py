#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证API并查询TBM项目的脚本
在禅道API启用后使用此脚本
"""

import os
import sys
import requests
import json

def load_config():
    """加载配置"""
    config = {}
    if os.path.exists('config.env'):
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    return config

def verify_api_working(base_url, username, password):
    """验证API是否正常工作"""
    print("🔍 验证API是否正常工作...")
    
    token_url = f"{base_url}/tokens"
    data = {"account": username, "password": password}
    
    try:
        response = requests.post(token_url, json=data, timeout=10)
        print(f"Token请求状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"✅ API正常工作，Token: {result['token'][:20]}...")
                    return result['token']
                else:
                    print(f"❌ 响应中没有token: {result}")
            except json.JSONDecodeError:
                print("❌ 响应不是有效JSON")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

def query_all_projects(base_url, token):
    """查询所有项目"""
    print("\n🔍 查询所有项目...")
    
    projects_url = f"{base_url}/projects"
    headers = {"Token": token}
    
    try:
        response = requests.get(projects_url, headers=headers, timeout=10)
        print(f"项目查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            projects = json.loads(response.text)
            print(f"✅ 获取到 {len(projects)} 个项目")
            
            # 显示所有项目
            print("\n📋 所有项目列表:")
            for i, project in enumerate(projects):
                name = project.get('name', '')
                project_id = project.get('id', '')
                status = project.get('status', '')
                print(f"  {i+1}. {name} (ID: {project_id}, 状态: {status})")
            
            return projects
        else:
            print(f"❌ 项目查询失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 项目查询异常: {e}")
    
    return []

def find_tbm_projects(projects):
    """查找TBM相关项目"""
    print("\n🎯 查找TBM相关项目...")
    
    tbm_keywords = ['TBM', 'tbm', '智能建造', '建造系统', '二期']
    tbm_projects = []
    
    for project in projects:
        name = project.get('name', '')
        for keyword in tbm_keywords:
            if keyword in name:
                tbm_projects.append(project)
                print(f"  ✅ 找到: {name} (ID: {project.get('id')})")
                break
    
    if not tbm_projects:
        print("  ❌ 未找到TBM相关项目")
        print("  💡 请检查项目名称是否包含关键词：TBM、智能建造等")
    
    return tbm_projects

def query_project_executions(base_url, token, project_id, project_name):
    """查询项目执行"""
    print(f"\n🔍 查询项目 '{project_name}' 的执行...")
    
    executions_url = f"{base_url}/executions"
    headers = {"Token": token}
    params = {"project": project_id}
    
    try:
        response = requests.get(executions_url, headers=headers, params=params, timeout=10)
        print(f"执行查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            executions = json.loads(response.text)
            print(f"✅ 找到 {len(executions)} 个执行")
            
            for execution in executions:
                name = execution.get('name', '')
                exec_id = execution.get('id', '')
                status = execution.get('status', '')
                percent = execution.get('percent', 0)
                begin = execution.get('begin', '')
                end = execution.get('end', '')
                
                print(f"\n📌 执行: {name} (ID: {exec_id})")
                print(f"   状态: {status}")
                print(f"   进度: {percent}%")
                print(f"   开始: {begin}")
                print(f"   结束: {end}")
                
                # 查询任务
                query_execution_tasks(base_url, token, exec_id, name)
            
            return executions
        else:
            print(f"❌ 执行查询失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 执行查询异常: {e}")
    
    return []

def query_execution_tasks(base_url, token, execution_id, execution_name):
    """查询执行任务"""
    print(f"   🔍 查询执行 '{execution_name}' 的任务...")
    
    tasks_url = f"{base_url}/tasks"
    headers = {"Token": token}
    params = {"execution": execution_id}
    
    try:
        response = requests.get(tasks_url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            tasks = json.loads(response.text)
            print(f"   ✅ 找到 {len(tasks)} 个任务")
            
            # 统计任务状态
            status_count = {}
            for task in tasks:
                status = task.get('status', 'unknown')
                status_count[status] = status_count.get(status, 0) + 1
            
            if status_count:
                print(f"   📊 任务状态统计:")
                for status, count in status_count.items():
                    print(f"      {status}: {count} 个")
            
            # 显示部分任务
            if tasks:
                print(f"   📝 任务列表 (前5个):")
                for i, task in enumerate(tasks[:5]):
                    name = task.get('name', '')
                    status = task.get('status', '')
                    assigned = task.get('assignedTo', '未指派')
                    print(f"      {i+1}. {name} - {status} (指派: {assigned})")
            
            return tasks
        else:
            print(f"   ❌ 任务查询失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 任务查询异常: {e}")
    
    return []

def main():
    """主函数"""
    print("========================================")
    print("禅道API验证和TBM项目查询")
    print("========================================")
    
    # 加载配置
    config = load_config()
    base_url = config.get('BASE_URL')
    username = config.get('USERNAME')
    password = config.get('PASSWORD')
    
    if not all([base_url, username, password]):
        print("❌ 配置不完整，请检查config.env文件")
        return
    
    print(f"🔧 配置信息:")
    print(f"  BASE_URL: {base_url}")
    print(f"  USERNAME: {username}")
    
    # 验证API
    token = verify_api_working(base_url, username, password)
    if not token:
        print("\n❌ API验证失败，请检查:")
        print("  1. 禅道API是否已启用")
        print("  2. 用户名密码是否正确")
        print("  3. 用户是否有API权限")
        return
    
    # 查询所有项目
    projects = query_all_projects(base_url, token)
    if not projects:
        print("\n❌ 无法获取项目列表")
        return
    
    # 查找TBM项目
    tbm_projects = find_tbm_projects(projects)
    if not tbm_projects:
        print("\n⚠️  未找到TBM相关项目")
        return
    
    # 查询每个TBM项目的执行情况
    print(f"\n{'='*60}")
    print("TBM项目执行情况详细报告")
    print(f"{'='*60}")
    
    for project in tbm_projects:
        project_name = project.get('name', '')
        project_id = project.get('id', '')
        
        print(f"\n📋 项目: {project_name}")
        print(f"ID: {project_id}")
        print(f"状态: {project.get('status', '')}")
        
        executions = query_project_executions(base_url, token, project_id, project_name)
        
        if executions:
            total_progress = sum(float(ex.get('percent', 0)) for ex in executions) / len(executions)
            print(f"\n📊 项目总体情况:")
            print(f"   执行数量: {len(executions)}")
            print(f"   平均进度: {total_progress:.1f}%")
    
    print(f"\n🎉 查询完成！")

if __name__ == "__main__":
    main()
