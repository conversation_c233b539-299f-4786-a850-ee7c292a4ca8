@REM @echo off
chcp 65001 >nul
echo "========================================"
echo "禅道MCP服务器一键安装脚本"
echo "========================================"
echo.

@REM 检查Python是否安装且版本为3.8+
for /f "tokens=2 delims= " %%A in ('python --version 2^>^&1') do (
    set "python_version=%%A"
)
if "%python_version%"=="" (
    echo "❌ 错误：未找到Python，请先安装Python 3.8+"
    echo "💡 下载地址：https://www.python.org/downloads/"
    pause
    exit /b 1
)

for /f "tokens=1,2 delims=." %%A in ("%python_version%") do (
    if %%A LSS 3 (
        echo "❌ 错误：Python版本过低（当前版本：%python_version%），请安装Python 3.8+"
        pause
        exit /b 1
    )
    if %%A EQU 3 if %%B LSS 8 (
        echo "❌ 错误：Python版本过低（当前版本：%python_version%），请安装Python 3.8+"
        pause
        exit /b 1
    )
)

echo "✅ Python已安装（版本：%python_version%）"

echo.
echo "📦 安装依赖包..."

REM 安装MCP依赖
pip install mcp >nul 2>&1
if %errorlevel% neq 0 (
    echo "❌ MCP安装失败，尝试使用国内镜像..."
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple mcp
    if %errorlevel% neq 0 (
        echo "❌ 依赖安装失败，请手动安装：pip install mcp"
        pause
        exit /b 1
    )
)
echo "✅ MCP已安装"

REM 安装requests（用于测试）
pip install requests >nul 2>&1
if %errorlevel% neq 0 (
    echo "⚠️  requests安装失败，测试功能可能受限"
) else (
    echo "✅ requests已安装"
)

echo.
echo "🔧 检查项目文件..."

if not exist "main.py" (
    echo "❌ 错误：未找到main.py文件"
    pause
    exit /b 1
)
echo "✅ main.py存在"

if not exist "service" (
    echo "❌ 错误：未找到service目录"
    pause
    exit /b 1
)
echo "✅ service目录存在"

echo.
echo "📝 创建配置文件..."

if not exist "config.env" (
    if exist "config.env.template" (
        copy "config.env.template" "config.env" >nul
        echo "✅ 已从模板创建config.env"
        echo "⚠️  请编辑config.env文件设置您的禅道配置"
    ) else (
        echo "❌ 未找到配置模板文件"
        pause
        exit /b 1
    )
) else (
    echo "✅ config.env已存在"
)

echo.
echo "🧪 运行测试..."
if not exist "test_mcp_server.py" (
    echo "⚠️  未找到测试脚本 test_mcp_server.py，跳过测试"
) else (
    python test_mcp_server.py
)

echo.
echo "========================================"
echo "安装完成！"
echo "========================================"
echo.
echo "📋 下一步操作："
echo "1. 编辑 config.env 文件，设置您的禅道配置"
echo "2. 运行 start_mcp_server.bat 启动服务器"
echo "3. 运行 generate_client_config.py 生成客户端配置"
echo "4. 在MCP客户端中配置连接"
echo.
echo "📖 详细说明请查看：使用说明.md"
echo.
pause
