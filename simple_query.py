#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的禅道查询脚本
"""

import os
import sys
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """加载配置文件"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    
    # 从环境变量加载配置
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if key in os.environ:
            config[key] = os.environ[key]
    
    return config

def test_connection():
    """测试连接"""
    print("🔍 测试禅道连接...")
    
    config = load_config()
    print(f"配置信息:")
    print(f"  BASE_URL: {config.get('BASE_URL', '未配置')}")
    print(f"  USERNAME: {config.get('USERNAME', '未配置')}")
    print(f"  PASSWORD: {'已配置' if config.get('PASSWORD') else '未配置'}")
    print(f"  TOKEN: {'已配置' if config.get('TOKEN') else '未配置'}")
    
    # 设置环境变量
    for key, value in config.items():
        os.environ[key] = value
    
    try:
        # 导入服务模块
        from service import project_service
        
        print("\n🔍 查询所有项目...")
        projects = project_service.list_all()
        
        print(f"✅ 成功获取 {len(projects)} 个项目:")
        
        # 查找TBM相关项目
        tbm_projects = []
        for i, project in enumerate(projects):
            project_name = project.get('name', '')
            print(f"  {i+1}. {project_name} (ID: {project.get('id')})")
            
            if 'TBM' in project_name or 'tbm' in project_name.lower():
                tbm_projects.append(project)
        
        if tbm_projects:
            print(f"\n🎯 找到 {len(tbm_projects)} 个TBM相关项目:")
            for project in tbm_projects:
                print(f"  - {project.get('name')} (ID: {project.get('id')})")
                
                # 查询该项目的执行
                try:
                    from service import execution_service
                    executions = execution_service.list_all(project.get('id'))
                    print(f"    执行数量: {len(executions)}")
                    
                    for execution in executions:
                        print(f"    📌 {execution.get('name')} - 状态: {execution.get('status')} - 进度: {execution.get('percent', 0)}%")
                        
                        # 查询任务
                        try:
                            from service import task_service
                            tasks = task_service.list_all(execution.get('id'))
                            print(f"      任务数量: {len(tasks)}")
                            
                            # 统计任务状态
                            status_count = {}
                            for task in tasks:
                                status = task.get('status', 'unknown')
                                status_count[status] = status_count.get(status, 0) + 1
                            
                            if status_count:
                                status_str = ', '.join([f"{k}: {v}" for k, v in status_count.items()])
                                print(f"      任务状态: {status_str}")
                                
                        except Exception as e:
                            print(f"      ❌ 查询任务失败: {e}")
                            
                except Exception as e:
                    print(f"    ❌ 查询执行失败: {e}")
        else:
            print("\n⚠️  未找到TBM相关项目")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("========================================")
    print("禅道项目查询测试")
    print("========================================")
    
    test_connection()

if __name__ == "__main__":
    main()
