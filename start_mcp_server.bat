@echo off
chcp 65001 >nul
echo ========================================
echo 禅道MCP服务器启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查main.py是否存在
if not exist "main.py" (
    echo ❌ 错误：未找到main.py文件，请确保在正确的目录下运行此脚本
    pause
    exit /b 1
)

REM 设置环境变量（如果config.env文件存在）
if exist "config.env" (
    echo 📝 加载配置文件 config.env...
    for /f "usebackq tokens=1,2 delims==" %%a in ("config.env") do (
        set "%%a=%%b"
    )
    echo ✅ 配置加载完成
) else (
    echo ⚠️  未找到config.env配置文件，使用默认配置
    echo 💡 提示：您可以创建config.env文件来设置环境变量
)

echo.
echo 🚀 启动禅道MCP服务器...
echo 📍 服务器地址：%BASE_URL%
echo 👤 用户名：%USERNAME%
echo 🔗 传输协议：stdio
echo.
echo 💡 提示：服务器启动后，请在MCP客户端中配置连接
echo 🛑 按 Ctrl+C 停止服务器
echo.

REM 启动MCP服务器
python main.py

echo.
echo 🛑 服务器已停止
pause
