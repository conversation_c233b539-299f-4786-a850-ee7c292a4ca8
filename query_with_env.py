#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带环境变量设置的禅道查询脚本
"""

import os
import sys

def load_and_set_config():
    """加载并设置环境变量"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        print("📝 从config.env加载配置...")
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    config[key] = value
                    # 设置环境变量
                    os.environ[key] = value
                    print(f"  {key}: {'已设置' if key != 'PASSWORD' else '***'}")
    else:
        print("⚠️  未找到config.env文件")
    
    # 检查必要的环境变量
    required_vars = ['BASE_URL']
    auth_vars = [('USERNAME', 'PASSWORD'), ('TOKEN',)]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    # 检查认证变量
    auth_ok = False
    if os.getenv('TOKEN'):
        auth_ok = True
        print("✅ 使用TOKEN认证")
    elif os.getenv('USERNAME') and os.getenv('PASSWORD'):
        auth_ok = True
        print("✅ 使用用户名密码认证")
    
    if not auth_ok:
        missing_vars.extend(['USERNAME+PASSWORD 或 TOKEN'])
    
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        return False
    
    return True

def query_projects():
    """查询项目"""
    print("\n🔍 查询所有项目...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from service import project_service
        
        projects = project_service.list_all()
        print(f"✅ 成功获取 {len(projects)} 个项目")
        
        # 查找TBM相关项目
        tbm_projects = []
        print("\n📋 所有项目列表:")
        for i, project in enumerate(projects):
            project_name = project.get('name', '')
            project_id = project.get('id', '')
            print(f"  {i+1}. {project_name} (ID: {project_id})")
            
            if 'TBM' in project_name or 'tbm' in project_name.lower() or '智能建造' in project_name:
                tbm_projects.append(project)
        
        return tbm_projects
        
    except Exception as e:
        print(f"❌ 查询项目失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def query_executions(project_id, project_name):
    """查询项目执行"""
    print(f"\n🔍 查询项目 '{project_name}' 的执行情况...")
    
    try:
        from service import execution_service
        
        executions = execution_service.list_all(project_id)
        print(f"✅ 找到 {len(executions)} 个执行")
        
        for execution in executions:
            exec_name = execution.get('name', '')
            exec_id = execution.get('id', '')
            exec_status = execution.get('status', '')
            exec_percent = execution.get('percent', 0)
            exec_begin = execution.get('begin', '')
            exec_end = execution.get('end', '')
            
            print(f"\n📌 执行: {exec_name} (ID: {exec_id})")
            print(f"   状态: {exec_status}")
            print(f"   进度: {exec_percent}%")
            print(f"   开始时间: {exec_begin}")
            print(f"   结束时间: {exec_end}")
            
            # 查询任务
            query_tasks(exec_id, exec_name)
        
        return executions
        
    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def query_tasks(execution_id, execution_name):
    """查询执行任务"""
    print(f"   🔍 查询执行 '{execution_name}' 的任务...")
    
    try:
        from service import task_service
        
        tasks = task_service.list_all(execution_id)
        print(f"   ✅ 找到 {len(tasks)} 个任务")
        
        if not tasks:
            return []
        
        # 统计任务状态
        status_count = {}
        for task in tasks:
            status = task.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
        
        print(f"   📊 任务状态统计:")
        for status, count in status_count.items():
            print(f"      {status}: {count} 个")
        
        # 显示最近的任务
        print(f"   📝 任务列表 (前10个):")
        for i, task in enumerate(tasks[:10]):
            task_name = task.get('name', '')
            task_status = task.get('status', '')
            task_assigned = task.get('assignedTo', '未指派')
            print(f"      {i+1}. {task_name} - {task_status} (指派: {task_assigned})")
        
        return tasks
        
    except Exception as e:
        print(f"   ❌ 查询任务失败: {e}")
        return []

def main():
    """主函数"""
    print("========================================")
    print("TBM智能建造系统(二期)项目查询")
    print("========================================")
    
    # 加载配置
    if not load_and_set_config():
        print("\n❌ 配置检查失败，请检查config.env文件")
        return
    
    print(f"\n🔗 连接到: {os.getenv('BASE_URL')}")
    print(f"👤 用户: {os.getenv('USERNAME', 'TOKEN认证')}")
    
    # 查询项目
    tbm_projects = query_projects()
    
    if not tbm_projects:
        print("\n❌ 未找到TBM或智能建造相关项目")
        return
    
    print(f"\n🎯 找到 {len(tbm_projects)} 个相关项目:")
    
    # 查询每个相关项目的执行情况
    for project in tbm_projects:
        project_name = project.get('name', '')
        project_id = project.get('id', '')
        
        print(f"\n{'='*60}")
        print(f"📋 项目: {project_name} (ID: {project_id})")
        print(f"{'='*60}")
        
        executions = query_executions(project_id, project_name)
        
        if executions:
            total_progress = sum(float(ex.get('percent', 0)) for ex in executions) / len(executions)
            print(f"\n📊 项目总体情况:")
            print(f"   执行数量: {len(executions)}")
            print(f"   平均进度: {total_progress:.1f}%")
    
    print(f"\n🎯 查询完成！")

if __name__ == "__main__":
    main()
