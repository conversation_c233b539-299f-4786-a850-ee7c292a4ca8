"""
执行相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

PROJECT_EXECUTIONS_URL = BASE_URL + '/projects/{}/executions'
EXECUTIONS_URL = BASE_URL + '/executions/{}'


def list_all(project_id):
    return req(Method.GET, PROJECT_EXECUTIONS_URL.format(project_id) + "?limit=1000", headers=HEADER)


def create(name, code, begin_time, end_time, project_id):
    data = {
        'name': name,
        'code': code,
        'begin': begin_time,
        'end': end_time
    }
    return req(Method.POST, PROJECT_EXECUTIONS_URL.format(project_id), headers=HEADER, body=data)


def get(execution_id):
    return req(Method.GET, EXECUTIONS_URL.format(execution_id), headers=HEADER)


def delete(execution_id):
    return req(Method.DELETE, EXECUTIONS_URL.format(execution_id), headers=HEADER)
