#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复禅道访问问题并查询TBM项目
"""

import requests
import json
import re
from urllib.parse import urljoin

def create_session():
    """创建已登录的会话"""
    print("🔐 创建已登录的会话...")
    
    base_url = "http://**********/zentao"
    session = requests.Session()
    
    try:
        # 登录
        login_url = f"{base_url}/index.php?m=user&f=login"
        login_data = {
            'account': 'admin',
            'password': 'ZLC#pkv5'
        }
        
        response = session.post(login_url, data=login_data, timeout=10)
        
        if 'zentaosid' in session.cookies:
            print("✅ 登录成功，获得会话")
            return session
        else:
            print("❌ 登录失败")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def try_different_project_urls(session):
    """尝试不同的项目访问URL"""
    print("\n🔍 尝试不同的项目访问方式...")
    
    base_url = "http://**********/zentao"
    
    # 不同的项目访问方式
    project_urls = [
        "/index.php?m=project&f=browse",
        "/project-browse.html",
        "/index.php/project/browse",
        "/zentao/project-browse.html",
        "/zentao/index.php?m=project&f=browse",
        "/www/index.php?m=project&f=browse",
        "/index.php?c=project&a=browse",
        "/project/browse",
        "/project",
    ]
    
    for project_url in project_urls:
        try:
            url = f"{base_url}{project_url}"
            response = session.get(url, timeout=5)
            print(f"  {project_url}: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # 检查是否包含项目信息
                if 'project' in content.lower() and len(content) > 1000:
                    print(f"    ✅ 包含项目内容")
                    
                    # 查找TBM项目
                    if 'TBM' in content or '智能建造' in content:
                        print(f"    🎯 找到TBM相关内容!")
                        return extract_project_info(content, url)
                    
                    # 即使没有TBM，也尝试提取项目信息
                    projects = extract_project_info(content, url)
                    if projects:
                        print(f"    📋 提取到 {len(projects)} 个项目")
                        return projects
                
                elif 'error' not in content.lower():
                    print(f"    内容长度: {len(content)}")
                    
        except Exception as e:
            print(f"  {project_url}: 失败 - {e}")
    
    return []

def extract_project_info(html_content, url):
    """从HTML内容中提取项目信息"""
    print(f"    📊 分析HTML内容...")
    
    projects = []
    
    # 查找表格中的项目信息
    table_patterns = [
        r'<tr[^>]*>.*?<td[^>]*>.*?(\d+).*?</td>.*?<td[^>]*>.*?<a[^>]*>([^<]+)</a>.*?</tr>',
        r'<tr[^>]*>.*?<td[^>]*>([^<]+)</td>.*?<td[^>]*>([^<]+)</td>.*?</tr>',
    ]
    
    for pattern in table_patterns:
        matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
        if matches:
            print(f"    找到 {len(matches)} 个表格匹配")
            for match in matches:
                if len(match) >= 2:
                    project_id = match[0].strip()
                    project_name = match[1].strip()
                    
                    if project_name and len(project_name) > 2:
                        projects.append({
                            'id': project_id,
                            'name': project_name,
                            'source': 'html_table'
                        })
                        print(f"      - {project_name} (ID: {project_id})")
    
    # 查找链接中的项目信息
    link_pattern = r'<a[^>]*href="[^"]*project[^"]*"[^>]*>([^<]+)</a>'
    link_matches = re.findall(link_pattern, html_content, re.IGNORECASE)
    
    if link_matches:
        print(f"    找到 {len(link_matches)} 个项目链接")
        for i, link_text in enumerate(link_matches):
            link_text = link_text.strip()
            if link_text and len(link_text) > 2 and '项目' in link_text:
                projects.append({
                    'id': f'link_{i}',
                    'name': link_text,
                    'source': 'html_link'
                })
                print(f"      - {link_text}")
    
    # 直接搜索TBM和智能建造
    tbm_pattern = r'([^<>\n]*(?:TBM|智能建造)[^<>\n]*)'
    tbm_matches = re.findall(tbm_pattern, html_content, re.IGNORECASE)
    
    if tbm_matches:
        print(f"    🎯 找到 {len(tbm_matches)} 个TBM相关匹配:")
        for match in tbm_matches:
            match = match.strip()
            if match and len(match) > 5:
                print(f"      🎯 {match}")
                projects.append({
                    'id': 'tbm_match',
                    'name': match,
                    'source': 'text_search'
                })
    
    return projects

def try_api_with_session(session):
    """使用会话尝试API访问"""
    print(f"\n🔍 使用会话尝试API访问...")
    
    base_url = "http://**********/zentao"
    
    # API访问方式
    api_urls = [
        "/api.php/v1/projects",
        "/api.php?m=project&f=getList&t=json",
        "/index.php?m=project&f=ajaxGetProjects&t=json",
        "/zentao/api.php/v1/projects",
        "/www/api.php/v1/projects",
    ]
    
    for api_url in api_urls:
        try:
            url = f"{base_url}{api_url}"
            response = session.get(url, timeout=5)
            print(f"  {api_url}: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"    响应长度: {len(content)}")
                
                # 尝试解析JSON
                try:
                    data = json.loads(content)
                    print(f"    ✅ 成功解析JSON，包含 {len(data)} 项")
                    
                    if isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict) and 'name' in item:
                                name = item.get('name', '')
                                if 'TBM' in name or '智能建造' in name:
                                    print(f"    🎯 找到TBM项目: {name}")
                    
                    return data
                    
                except json.JSONDecodeError:
                    if len(content) < 500:
                        print(f"    非JSON响应: {content}")
                    else:
                        print(f"    非JSON响应，长度: {len(content)}")
                        
        except Exception as e:
            print(f"  {api_url}: 失败 - {e}")
    
    return None

def main():
    """主函数"""
    print("========================================")
    print("修复禅道访问并查询TBM项目")
    print("========================================")
    
    # 创建会话
    session = create_session()
    if not session:
        print("❌ 无法创建会话")
        return
    
    # 尝试API访问
    api_data = try_api_with_session(session)
    
    if api_data:
        print(f"\n✅ 通过API获取数据成功")
    else:
        print(f"\n⚠️  API访问失败，尝试HTML解析...")
        
        # 尝试HTML访问
        projects = try_different_project_urls(session)
        
        if projects:
            print(f"\n📋 通过HTML解析找到项目:")
            
            tbm_projects = []
            for project in projects:
                name = project.get('name', '')
                if 'TBM' in name or '智能建造' in name or 'tbm' in name.lower():
                    tbm_projects.append(project)
                    print(f"🎯 TBM项目: {name} (ID: {project.get('id')})")
            
            if tbm_projects:
                print(f"\n🎉 找到 {len(tbm_projects)} 个TBM相关项目!")
                
                # 尝试获取更多详细信息
                for project in tbm_projects:
                    print(f"\n📋 项目详情: {project['name']}")
                    print(f"   ID: {project['id']}")
                    print(f"   来源: {project['source']}")
                    
                    # 如果有项目ID，尝试获取执行信息
                    if project['id'].isdigit():
                        try_get_executions(session, project['id'], project['name'])
            else:
                print(f"\n⚠️  未找到TBM相关项目")
                print(f"所有项目:")
                for project in projects[:10]:  # 只显示前10个
                    print(f"  - {project['name']}")
        else:
            print(f"\n❌ 无法获取项目信息")

def try_get_executions(session, project_id, project_name):
    """尝试获取项目执行信息"""
    print(f"    🔍 查询项目执行...")
    
    base_url = "http://**********/zentao"
    
    execution_urls = [
        f"/index.php?m=execution&f=browse&projectID={project_id}",
        f"/execution-browse-{project_id}.html",
        f"/index.php?m=project&f=execution&projectID={project_id}",
    ]
    
    for exec_url in execution_urls:
        try:
            url = f"{base_url}{exec_url}"
            response = session.get(url, timeout=5)
            
            if response.status_code == 200:
                content = response.text
                if 'execution' in content.lower() and len(content) > 1000:
                    print(f"    ✅ 获取到执行页面")
                    
                    # 简单提取执行信息
                    if '进度' in content or 'progress' in content.lower():
                        print(f"    📊 页面包含进度信息")
                    
                    return True
                    
        except Exception as e:
            continue
    
    print(f"    ❌ 无法获取执行信息")
    return False

if __name__ == "__main__":
    main()
