#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查禅道版本和API支持情况
"""

import requests
import re

def check_zentao_version():
    """检查禅道版本"""
    print("🔍 检查禅道版本和API支持...")
    
    base_url = "http://10.95.2.86/zentao"
    
    try:
        # 获取主页
        response = requests.get(base_url, timeout=10)
        print(f"主页访问: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # 查找版本信息
            version_patterns = [
                r'zentao[^>]*?(\d+\.\d+[^<\s]*)',
                r'version[^>]*?(\d+\.\d+[^<\s]*)',
                r'v(\d+\.\d+[^<\s]*)',
                r'禅道.*?(\d+\.\d+[^<\s]*)',
            ]
            
            version_found = False
            for pattern in version_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"✅ 可能的版本: {matches}")
                    version_found = True
            
            if not version_found:
                print("⚠️  未在主页找到明确的版本信息")
            
            # 检查API相关信息
            api_indicators = ['api', 'rest', 'json', 'token']
            found_apis = []
            for indicator in api_indicators:
                if indicator in content.lower():
                    found_apis.append(indicator)
            
            if found_apis:
                print(f"✅ 页面包含API相关内容: {found_apis}")
            else:
                print("⚠️  页面未发现明显的API相关内容")
        
        # 检查特定的API文件
        api_files = [
            "/api.php",
            "/index.php",
            "/www/api.php",
            "/framework/api.php"
        ]
        
        print(f"\n检查API文件:")
        for api_file in api_files:
            try:
                url = f"{base_url}{api_file}"
                response = requests.get(url, timeout=5)
                print(f"  {api_file}: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text[:300]
                    if 'api' in content.lower() or 'json' in content.lower():
                        print(f"    ✅ 可能支持API")
                    else:
                        print(f"    内容: {content}")
                        
            except Exception as e:
                print(f"  {api_file}: 访问失败")
        
        # 尝试不同的API调用方式
        print(f"\n尝试不同的API调用:")
        
        # 方式1: 直接调用
        test_urls = [
            "/api.php?m=project&f=getList",
            "/index.php?m=project&f=browse&t=json",
            "/index.php?m=api&f=getModel&moduleID=project",
            "/?m=project&f=browse&t=json"
        ]
        
        for test_url in test_urls:
            try:
                url = f"{base_url}{test_url}"
                response = requests.get(url, timeout=5)
                print(f"  {test_url}: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    if len(content) < 1000 and ('json' in content.lower() or '{' in content):
                        print(f"    可能的JSON: {content[:200]}")
                    elif 'project' in content.lower() and len(content) > 1000:
                        print(f"    ✅ 包含项目相关内容")
                        
                        # 尝试查找项目信息
                        if 'TBM' in content or '智能建造' in content:
                            print(f"    🎯 页面包含TBM或智能建造相关内容!")
                    else:
                        print(f"    内容长度: {len(content)}")
                        
            except Exception as e:
                print(f"  {test_url}: 失败")
    
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")

def try_simple_login():
    """尝试简单登录"""
    print(f"\n🔍 尝试简单登录...")
    
    base_url = "http://10.95.2.86/zentao"
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 先访问登录页面
        login_page = f"{base_url}/index.php?m=user&f=login"
        response = session.get(login_page, timeout=10)
        print(f"登录页面: {response.status_code}")
        
        if response.status_code == 200:
            # 尝试登录
            login_data = {
                'account': 'admin',
                'password': 'ZLC#pkv5'
            }
            
            response = session.post(login_page, data=login_data, timeout=10)
            print(f"登录提交: {response.status_code}")
            
            # 检查是否有会话Cookie
            cookies = session.cookies
            print(f"获得的Cookies: {list(cookies.keys())}")
            
            if cookies:
                # 尝试访问项目页面
                project_page = f"{base_url}/index.php?m=project&f=browse"
                response = session.get(project_page, timeout=10)
                print(f"项目页面: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 查找项目信息
                    if 'TBM' in content:
                        print(f"🎯 在项目页面找到TBM!")
                        
                        # 提取TBM相关信息
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if 'TBM' in line:
                                print(f"    第{i}行: {line.strip()[:100]}")
                    
                    if '智能建造' in content:
                        print(f"🎯 在项目页面找到智能建造!")
                        
                        # 提取智能建造相关信息
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if '智能建造' in line:
                                print(f"    第{i}行: {line.strip()[:100]}")
                    
                    # 尝试JSON格式
                    json_page = f"{base_url}/index.php?m=project&f=browse&t=json"
                    response = session.get(json_page, timeout=10)
                    print(f"JSON项目页面: {response.status_code}")
                    
                    if response.status_code == 200:
                        json_content = response.text
                        print(f"JSON内容长度: {len(json_content)}")
                        print(f"JSON内容预览: {json_content[:300]}")
                        
                        try:
                            import json
                            data = json.loads(json_content)
                            print(f"✅ 成功解析JSON，包含 {len(data)} 项")
                            
                            # 查找TBM项目
                            for item in data:
                                if isinstance(item, dict):
                                    name = item.get('name', '')
                                    if 'TBM' in name or '智能建造' in name:
                                        print(f"🎯 找到TBM项目: {name}")
                                        print(f"    项目信息: {item}")
                                        
                        except json.JSONDecodeError:
                            print(f"❌ JSON解析失败")
    
    except Exception as e:
        print(f"❌ 登录尝试失败: {e}")

def main():
    """主函数"""
    print("========================================")
    print("禅道版本和API支持检查")
    print("========================================")
    
    check_zentao_version()
    try_simple_login()

if __name__ == "__main__":
    main()
