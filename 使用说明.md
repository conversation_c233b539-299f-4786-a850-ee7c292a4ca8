# 禅道MCP服务器使用说明

## 📋 概述

禅道MCP服务器是一个基于MCP（Model Context Protocol）的服务器，提供与禅道项目管理系统的集成功能。

## 🚀 快速开始

### 1. 配置服务器

运行配置向导：
```bash
setup_config.bat
```

或者手动创建 `config.env` 文件：
```env
BASE_URL=http://127.0.0.1/zentao/api.php/v1
USERNAME=admin
PASSWORD=Zentao@123
LOG_LEVEL=ERROR
```

### 2. 启动服务器

```bash
start_mcp_server.bat
```

### 3. 测试服务器

```bash
python test_mcp_server.py
```

### 4. 生成客户端配置

```bash
python generate_client_config.py
```

## 📁 脚本说明

### 启动脚本
- `start_mcp_server.bat` - Windows批处理启动脚本
- 自动加载配置文件
- 显示启动状态和配置信息

### 配置脚本
- `setup_config.bat` - 交互式配置向导
- `config.env.template` - 配置文件模板
- 支持用户名密码或TOKEN认证

### 测试脚本
- `test_mcp_server.py` - 全面的功能测试
- 测试依赖、配置、启动、连接
- 自动生成客户端配置

### 配置生成器
- `generate_client_config.py` - 生成各种MCP客户端配置
- 支持Claude Desktop、Cline、Continue等客户端

## 🔧 配置选项

### 环境变量

| 变量名 | 说明 | 示例 |
|--------|------|------|
| BASE_URL | 禅道API地址 | http://127.0.0.1/zentao/api.php/v1 |
| USERNAME | 用户名 | admin |
| PASSWORD | 密码 | Zentao@123 |
| TOKEN | 认证TOKEN（可选） | your_token_here |
| LOG_LEVEL | 日志级别 | ERROR/WARNING/INFO/DEBUG |

### 认证方式

1. **用户名密码**（推荐）
   ```env
   USERNAME=admin
   PASSWORD=Zentao@123
   ```

2. **TOKEN**
   ```env
   TOKEN=your_token_here
   ```

## 📱 客户端配置

### Claude Desktop

将生成的配置添加到 `~/.claude_desktop_config.json`：
```json
{
  "mcpServers": {
    "mcp-server-zentao": {
      "command": "python",
      "args": ["path/to/main.py"],
      "env": {
        "BASE_URL": "http://127.0.0.1/zentao/api.php/v1",
        "USERNAME": "admin",
        "PASSWORD": "Zentao@123"
      }
    }
  }
}
```

### Cline (VSCode扩展)

在Cline设置中添加MCP服务器配置。

### Continue

在Continue配置文件中添加MCP服务器配置。

## 🛠️ 功能列表

### 用户管理
- 创建用户
- 查询用户列表
- 查询用户详情
- 删除用户
- 修改用户信息

### 产品管理
- 创建产品
- 查询产品列表
- 查询产品详情
- 删除产品

### 项目管理
- 创建项目
- 查询项目列表
- 查询项目详情
- 删除项目

### 执行管理
- 创建项目执行
- 查询执行列表
- 查询执行详情
- 删除执行

### 任务管理
- 创建任务
- 查询任务列表
- 查询任务详情
- 删除任务
- 修改任务信息

### 需求管理
- 创建需求
- 查询需求列表
- 查询需求详情
- 删除需求
- 修改需求信息

### BUG管理
- 创建BUG
- 查询BUG列表
- 查询BUG详情
- 删除BUG
- 修改BUG信息

## 🔍 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查Python是否正确安装
   - 检查依赖是否安装：`pip install mcp`
   - 检查配置文件是否正确

2. **连接禅道失败**
   - 检查BASE_URL是否正确
   - 检查禅道服务器是否运行
   - 检查用户名密码是否正确

3. **客户端连接失败**
   - 检查客户端配置是否正确
   - 检查Python路径是否正确
   - 检查脚本路径是否正确

### 调试模式

设置日志级别为DEBUG：
```env
LOG_LEVEL=DEBUG
```

## 📞 支持

如有问题，请检查：
1. 运行测试脚本：`python test_mcp_server.py`
2. 查看日志输出
3. 检查配置文件
4. 确认禅道服务器状态
