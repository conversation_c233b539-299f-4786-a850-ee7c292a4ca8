#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询TBM智能建造系统(二期)项目执行情况
"""

import os
import sys
import json
import requests
from service.token_service import get_token
from service.project_service import list_all as list_projects
from service.execution_service import list_all as list_executions
from service.task_service import list_all as list_tasks

def load_config():
    """加载配置文件"""
    config = {}
    
    # 从config.env文件加载配置
    if os.path.exists('config.env'):
        with open('config.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    
    # 从环境变量加载配置
    for key in ['BASE_URL', 'USERNAME', 'PASSWORD', 'TOKEN']:
        if key in os.environ:
            config[key] = os.environ[key]
    
    return config

def find_tbm_project():
    """查找TBM智能建造系统(二期)项目"""
    print("🔍 正在查找TBM智能建造系统(二期)项目...")
    
    try:
        projects = list_projects()
        print(f"📋 找到 {len(projects)} 个项目")
        
        tbm_projects = []
        for project in projects:
            if 'TBM' in project.get('name', '') or 'tbm' in project.get('name', '').lower():
                tbm_projects.append(project)
                print(f"🎯 找到相关项目: {project.get('name')} (ID: {project.get('id')})")
        
        # 查找完全匹配的项目
        target_project = None
        for project in tbm_projects:
            if 'TBM智能建造系统(二期)' in project.get('name', ''):
                target_project = project
                break
        
        if target_project:
            print(f"✅ 找到目标项目: {target_project.get('name')} (ID: {target_project.get('id')})")
            return target_project
        elif tbm_projects:
            print("⚠️  未找到完全匹配的项目，但找到相关项目:")
            for i, project in enumerate(tbm_projects):
                print(f"   {i+1}. {project.get('name')} (ID: {project.get('id')})")
            return tbm_projects[0]  # 返回第一个相关项目
        else:
            print("❌ 未找到TBM相关项目")
            return None
            
    except Exception as e:
        print(f"❌ 查询项目失败: {e}")
        return None

def query_project_executions(project_id):
    """查询项目的执行情况"""
    print(f"\n🔍 正在查询项目 {project_id} 的执行情况...")
    
    try:
        executions = list_executions(project_id)
        print(f"📋 找到 {len(executions)} 个执行")
        
        for execution in executions:
            print(f"\n📌 执行: {execution.get('name')} (ID: {execution.get('id')})")
            print(f"   状态: {execution.get('status')}")
            print(f"   开始时间: {execution.get('begin')}")
            print(f"   结束时间: {execution.get('end')}")
            print(f"   进度: {execution.get('percent', 0)}%")
            
            # 查询执行的任务
            query_execution_tasks(execution.get('id'))
        
        return executions
        
    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
        return []

def query_execution_tasks(execution_id):
    """查询执行的任务情况"""
    print(f"   🔍 查询执行 {execution_id} 的任务...")
    
    try:
        tasks = list_tasks(execution_id)
        print(f"   📋 找到 {len(tasks)} 个任务")
        
        # 统计任务状态
        status_count = {}
        for task in tasks:
            status = task.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
        
        print(f"   📊 任务状态统计:")
        for status, count in status_count.items():
            print(f"      {status}: {count} 个")
        
        # 显示最近的几个任务
        if tasks:
            print(f"   📝 最近任务:")
            for i, task in enumerate(tasks[:5]):  # 只显示前5个任务
                print(f"      {i+1}. {task.get('name')} - {task.get('status')} (指派给: {task.get('assignedTo', '未指派')})")
        
        return tasks
        
    except Exception as e:
        print(f"   ❌ 查询任务失败: {e}")
        return []

def main():
    """主函数"""
    print("========================================")
    print("TBM智能建造系统(二期)项目查询")
    print("========================================")
    
    # 加载配置
    config = load_config()
    if not config.get('BASE_URL'):
        print("❌ 未找到配置，请先配置环境变量")
        return
    
    print(f"🔗 连接到: {config.get('BASE_URL')}")
    print(f"👤 用户: {config.get('USERNAME', 'TOKEN认证')}")
    
    # 查找TBM项目
    project = find_tbm_project()
    if not project:
        print("\n❌ 未找到TBM项目，请检查项目名称")
        return
    
    # 查询项目执行情况
    executions = query_project_executions(project.get('id'))
    
    # 生成报告
    print("\n" + "="*50)
    print("📊 项目执行情况报告")
    print("="*50)
    print(f"项目名称: {project.get('name')}")
    print(f"项目ID: {project.get('id')}")
    print(f"项目状态: {project.get('status')}")
    print(f"执行数量: {len(executions)}")
    
    if executions:
        total_progress = sum(float(ex.get('percent', 0)) for ex in executions) / len(executions)
        print(f"平均进度: {total_progress:.1f}%")
    
    print("\n🎯 查询完成！")

if __name__ == "__main__":
    main()
