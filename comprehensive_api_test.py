#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的禅道API测试
尝试不同的请求方式和参数格式
"""

import requests
import json
import urllib.parse

def test_token_methods():
    """测试不同的Token获取方法"""
    print("🔍 测试不同的Token获取方法...")
    
    base_url = "http://**********/zentao"
    username = "admin"
    password = "ZLC#pkv5"
    
    # 方法1: 标准JSON POST
    print("\n1. 标准JSON POST方法:")
    try:
        url = f"{base_url}/api.php/v1/tokens"
        data = {"account": username, "password": password}
        response = requests.post(url, json=data, timeout=10)
        print(f"   URL: {url}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}")
        
        if response.status_code == 200 and response.text.strip():
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"   ✅ 成功获取Token: {result['token'][:20]}...")
                    return result['token']
            except json.JSONDecodeError:
                pass
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    # 方法2: 表单数据POST
    print("\n2. 表单数据POST方法:")
    try:
        url = f"{base_url}/api.php/v1/tokens"
        data = {"account": username, "password": password}
        response = requests.post(url, data=data, timeout=10)
        print(f"   URL: {url}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}")
        
        if response.status_code == 200 and response.text.strip():
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"   ✅ 成功获取Token: {result['token'][:20]}...")
                    return result['token']
            except json.JSONDecodeError:
                pass
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    # 方法3: GET请求带参数
    print("\n3. GET请求带参数:")
    try:
        url = f"{base_url}/api.php/v1/tokens"
        params = {"account": username, "password": password}
        response = requests.get(url, params=params, timeout=10)
        print(f"   URL: {url}?{urllib.parse.urlencode(params)}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}")
        
        if response.status_code == 200 and response.text.strip():
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"   ✅ 成功获取Token: {result['token'][:20]}...")
                    return result['token']
            except json.JSONDecodeError:
                pass
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    # 方法4: 不同的API路径
    api_paths = [
        "/api.php/v1/tokens",
        "/api/v1/tokens", 
        "/index.php?m=api&f=getSessionID",
        "/api.php/tokens",
        "/api/tokens"
    ]
    
    print("\n4. 尝试不同的API路径:")
    for path in api_paths:
        try:
            url = f"{base_url}{path}"
            data = {"account": username, "password": password}
            
            # 尝试POST
            response = requests.post(url, json=data, timeout=5)
            print(f"   POST {path}: {response.status_code}")
            
            if response.status_code == 200 and response.text.strip():
                try:
                    result = json.loads(response.text)
                    if 'token' in result:
                        print(f"   ✅ 成功获取Token: {result['token'][:20]}...")
                        return result['token']
                    else:
                        print(f"   响应: {response.text[:100]}")
                except json.JSONDecodeError:
                    print(f"   非JSON响应: {response.text[:100]}")
            
        except Exception as e:
            print(f"   ❌ {path} 失败: {e}")
    
    # 方法5: 基础认证
    print("\n5. 基础认证方法:")
    try:
        url = f"{base_url}/api.php/v1/tokens"
        auth = (username, password)
        response = requests.post(url, auth=auth, timeout=10)
        print(f"   URL: {url}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}")
        
        if response.status_code == 200 and response.text.strip():
            try:
                result = json.loads(response.text)
                if 'token' in result:
                    print(f"   ✅ 成功获取Token: {result['token'][:20]}...")
                    return result['token']
            except json.JSONDecodeError:
                pass
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    return None

def test_direct_api_access():
    """测试直接API访问（无Token）"""
    print("\n🔍 测试直接API访问（无Token）...")
    
    base_url = "http://**********/zentao"
    
    # 尝试直接访问项目API
    api_endpoints = [
        "/api.php/v1/projects",
        "/api/v1/projects",
        "/index.php?m=project&f=browse&t=json",
        "/index.php?m=api&f=getModel&moduleID=project&methodName=getList",
    ]
    
    for endpoint in api_endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            print(f"   GET {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:200]
                print(f"   响应: {content}")
                
                # 检查是否是JSON
                try:
                    json_data = json.loads(response.text)
                    print(f"   ✅ 返回有效JSON，包含 {len(json_data)} 项")
                    if isinstance(json_data, list) and len(json_data) > 0:
                        print(f"   第一项: {json_data[0]}")
                        return json_data
                except json.JSONDecodeError:
                    if 'html' not in content.lower():
                        print(f"   可能的数据: {content}")
            
        except Exception as e:
            print(f"   ❌ {endpoint} 失败: {e}")
    
    return None

def test_session_based_access():
    """测试基于会话的访问"""
    print("\n🔍 测试基于会话的访问...")
    
    base_url = "http://**********/zentao"
    username = "admin"
    password = "ZLC#pkv5"
    
    # 创建会话
    session = requests.Session()
    
    # 尝试登录
    login_urls = [
        "/index.php?m=user&f=login",
        "/user-login.html",
        "/index.php?m=user&f=ajaxLogin"
    ]
    
    for login_url in login_urls:
        try:
            url = f"{base_url}{login_url}"
            
            # 先GET获取页面
            response = session.get(url, timeout=5)
            print(f"   GET {login_url}: {response.status_code}")
            
            if response.status_code == 200:
                # 尝试POST登录
                login_data = {
                    'account': username,
                    'password': password,
                    'referer': '',
                    'verifyRand': ''
                }
                
                response = session.post(url, data=login_data, timeout=5)
                print(f"   POST {login_url}: {response.status_code}")
                
                # 检查是否登录成功（通过Cookie或重定向）
                if 'zentaosid' in session.cookies or response.status_code == 302:
                    print(f"   ✅ 可能登录成功")
                    
                    # 尝试访问项目页面
                    project_url = f"{base_url}/index.php?m=project&f=browse"
                    response = session.get(project_url, timeout=5)
                    print(f"   访问项目页面: {response.status_code}")
                    
                    if response.status_code == 200:
                        content = response.text
                        if 'project' in content.lower() and 'html' in content.lower():
                            print(f"   ✅ 成功访问项目页面")
                            
                            # 尝试JSON格式
                            json_url = f"{base_url}/index.php?m=project&f=browse&t=json"
                            response = session.get(json_url, timeout=5)
                            print(f"   JSON格式: {response.status_code}")
                            print(f"   JSON响应: {response.text[:200]}")
                            
                            try:
                                json_data = json.loads(response.text)
                                print(f"   ✅ 获取到JSON数据")
                                return session, json_data
                            except json.JSONDecodeError:
                                pass
                    
        except Exception as e:
            print(f"   ❌ {login_url} 失败: {e}")
    
    return None, None

def main():
    """主函数"""
    print("========================================")
    print("全面的禅道API测试")
    print("========================================")
    
    # 测试Token方法
    token = test_token_methods()
    
    if token:
        print(f"\n🎉 成功获取Token: {token[:20]}...")
        
        # 使用Token测试API
        base_url = "http://**********/zentao/api.php/v1"
        headers = {"Token": token}
        
        try:
            response = requests.get(f"{base_url}/projects", headers=headers, timeout=10)
            print(f"\n使用Token访问项目API: {response.status_code}")
            print(f"响应: {response.text[:200]}")
            
            if response.status_code == 200:
                projects = json.loads(response.text)
                print(f"✅ 获取到 {len(projects)} 个项目")
                
                # 查找TBM项目
                for project in projects:
                    name = project.get('name', '')
                    if 'TBM' in name or '智能建造' in name:
                        print(f"🎯 找到TBM项目: {name} (ID: {project.get('id')})")
                
        except Exception as e:
            print(f"❌ 使用Token访问失败: {e}")
    
    else:
        print(f"\n❌ 无法获取Token，尝试其他方法...")
        
        # 测试直接API访问
        data = test_direct_api_access()
        
        if not data:
            # 测试会话访问
            session, data = test_session_based_access()
            
            if data:
                print(f"✅ 通过会话访问成功")
            else:
                print(f"❌ 所有方法都失败了")
                print(f"\n💡 建议检查:")
                print(f"  1. 禅道版本和API支持情况")
                print(f"  2. API配置和权限设置")
                print(f"  3. 用户权限和密码正确性")

if __name__ == "__main__":
    main()
