#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替代的MCP服务器实现
绕过禅道API问题，直接使用会话访问
"""

import os
import sys
import requests
import json
import re
from mcp.server.fastmcp import FastMCP

# 全局会话对象
zentao_session = None
base_url = "http://10.95.2.86/zentao"

def create_zentao_session():
    """创建禅道会话"""
    global zentao_session
    
    if zentao_session is None:
        zentao_session = requests.Session()
        
        # 登录
        login_url = f"{base_url}/index.php?m=user&f=login"
        login_data = {
            'account': 'admin',
            'password': 'ZLC#pkv5'
        }
        
        try:
            response = zentao_session.post(login_url, data=login_data, timeout=10)
            if 'zentaosid' in zentao_session.cookies:
                print("✅ 禅道会话创建成功")
                return True
            else:
                print("❌ 禅道登录失败")
                return False
        except Exception as e:
            print(f"❌ 禅道连接失败: {e}")
            return False
    
    return True

def get_projects_from_html():
    """从HTML页面获取项目信息"""
    if not create_zentao_session():
        return []
    
    try:
        # 尝试访问项目页面
        project_urls = [
            "/index.php?m=project&f=browse",
            "/project-browse.html",
        ]
        
        for project_url in project_urls:
            try:
                url = f"{base_url}{project_url}"
                response = zentao_session.get(url, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 提取项目信息
                    projects = []
                    
                    # 查找项目表格
                    table_pattern = r'<tr[^>]*>.*?<td[^>]*>.*?(\d+).*?</td>.*?<td[^>]*>.*?<a[^>]*>([^<]+)</a>.*?</tr>'
                    matches = re.findall(table_pattern, content, re.DOTALL | re.IGNORECASE)
                    
                    for match in matches:
                        if len(match) >= 2:
                            project_id = match[0].strip()
                            project_name = match[1].strip()
                            
                            if project_name and len(project_name) > 2:
                                projects.append({
                                    'id': project_id,
                                    'name': project_name,
                                    'status': 'active'  # 默认状态
                                })
                    
                    if projects:
                        return projects
                        
            except Exception as e:
                continue
        
        # 如果表格解析失败，尝试简单的文本搜索
        return get_projects_by_text_search()
        
    except Exception as e:
        print(f"获取项目失败: {e}")
        return []

def get_projects_by_text_search():
    """通过文本搜索获取项目"""
    try:
        url = f"{base_url}/index.php?m=project&f=browse"
        response = zentao_session.get(url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 搜索包含项目关键词的文本
            project_patterns = [
                r'([^<>\n]*(?:TBM|智能建造|系统|项目)[^<>\n]*)',
                r'<a[^>]*>([^<]*(?:TBM|智能建造|系统)[^<]*)</a>',
            ]
            
            projects = []
            project_id = 1
            
            for pattern in project_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    match = match.strip()
                    if match and len(match) > 5 and '项目' in match:
                        projects.append({
                            'id': str(project_id),
                            'name': match,
                            'status': 'active'
                        })
                        project_id += 1
            
            return projects
            
    except Exception as e:
        print(f"文本搜索失败: {e}")
        return []

# 创建MCP服务器
mcp = FastMCP("ZenTao-Alternative", log_level="ERROR")

@mcp.tool("获取项目列表")
def list_projects():
    """
    获取所有项目列表（使用HTML解析）
    """
    try:
        projects = get_projects_from_html()
        return {
            "status": "success",
            "data": projects,
            "count": len(projects),
            "message": f"成功获取 {len(projects)} 个项目"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取项目失败: {str(e)}"
        }

@mcp.tool("查找TBM项目")
def find_tbm_projects():
    """
    查找TBM智能建造系统相关项目
    """
    try:
        all_projects = get_projects_from_html()
        
        # 查找TBM相关项目
        tbm_projects = []
        keywords = ['TBM', 'tbm', '智能建造', '建造系统', '二期']
        
        for project in all_projects:
            name = project.get('name', '')
            for keyword in keywords:
                if keyword in name:
                    tbm_projects.append(project)
                    break
        
        return {
            "status": "success",
            "data": tbm_projects,
            "count": len(tbm_projects),
            "message": f"找到 {len(tbm_projects)} 个TBM相关项目"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"查找TBM项目失败: {str(e)}"
        }

@mcp.tool("获取项目详情")
def get_project_detail(project_id: str):
    """
    获取项目详细信息
    :param project_id 项目ID
    """
    try:
        if not create_zentao_session():
            return {"status": "error", "message": "无法连接禅道"}
        
        # 尝试访问项目详情页面
        detail_urls = [
            f"/index.php?m=project&f=view&projectID={project_id}",
            f"/project-view-{project_id}.html",
        ]
        
        for detail_url in detail_urls:
            try:
                url = f"{base_url}{detail_url}"
                response = zentao_session.get(url, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 提取项目详情
                    detail = {
                        "id": project_id,
                        "status": "active",
                        "description": "通过HTML解析获取"
                    }
                    
                    # 尝试提取项目名称
                    name_pattern = r'<title>([^<]*)</title>'
                    name_match = re.search(name_pattern, content)
                    if name_match:
                        detail["name"] = name_match.group(1).strip()
                    
                    # 查找进度信息
                    if '进度' in content or 'progress' in content.lower():
                        detail["has_progress"] = True
                    
                    return {
                        "status": "success",
                        "data": detail
                    }
                    
            except Exception as e:
                continue
        
        return {
            "status": "error",
            "message": f"无法获取项目 {project_id} 的详情"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取项目详情失败: {str(e)}"
        }

@mcp.tool("测试禅道连接")
def test_zentao_connection():
    """
    测试禅道连接状态
    """
    try:
        if create_zentao_session():
            return {
                "status": "success",
                "message": "禅道连接正常",
                "base_url": base_url,
                "session_active": True
            }
        else:
            return {
                "status": "error",
                "message": "禅道连接失败"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": f"连接测试失败: {str(e)}"
        }

if __name__ == "__main__":
    print("启动替代MCP服务器...")
    print("测试禅道连接...")
    
    # 测试连接
    if create_zentao_session():
        print("✅ 禅道连接成功")
        
        # 测试获取项目
        projects = get_projects_from_html()
        print(f"✅ 获取到 {len(projects)} 个项目")
        
        # 查找TBM项目
        tbm_projects = []
        keywords = ['TBM', 'tbm', '智能建造', '建造系统', '二期']
        
        for project in projects:
            name = project.get('name', '')
            for keyword in keywords:
                if keyword in name:
                    tbm_projects.append(project)
                    print(f"🎯 找到TBM项目: {name} (ID: {project.get('id')})")
                    break
        
        if tbm_projects:
            print(f"🎉 总共找到 {len(tbm_projects)} 个TBM相关项目")
        else:
            print("⚠️  未找到TBM相关项目")
            print("所有项目:")
            for project in projects[:10]:
                print(f"  - {project.get('name')} (ID: {project.get('id')})")
    
    print("\n🚀 启动MCP服务器...")
    mcp.run(transport='stdio')
