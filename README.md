# mcp-server-zentao

#### 介绍
版本：V0.0.3

mcp-server-zentao是一个面向禅道API开发的MCP Server，其核心目标是简化工具链集成与接口标准化，帮助开发者快速构建支持自然语言交互的禅道智能体应用。

#### 功能
 **用户管理** 

创建用户、查询用户列表、删除用户、查询用户详情、修改用户

 **产品管理** 

创建产品、查询产品列表、删除产品、查询产品详情

 **项目管理** 

创建项目、查询项目列表、删除项目、查询项目详情

 **执行管理** 

创建项目执行、查询项目执行列表、删除项目执行、查询项目执行详情

 **任务管理** 

创建任务、查询项目执行任务列表、删除任务、查询任务详情、修改任务

 **需求管理** 

查询需求列表、创建需求、查询需求详情、删除需求、修改需求

 **BUG管理** 

查询BUG列表、创建BUG、查询BUG详情、删除BUG、修改BUG

#### 使用说明

 **MCP客户端配置示例** 
```
"mcp-server-zentao": {
      "disabled": false,
      "timeout": 60,
      "command": "D:\\pycharm-workspace\\mcp-server-zentao\\venv\\Scripts\\python",
      "args": [
        "D:\\pycharm-workspace\\mcp-server-zentao\\main.py"
      ],
      "env": {
        "BASE_URL": "http://127.0.0.1/zentao/api.php/v1",
        "USERNAME": "admin",
        "PASSWORD": "Zentao@123"
      },
      "transportType": "stdio",
      "autoApprove": []
}
```

 **参数解释** 
env：自定义的环境参数
- BASE_URL：禅道API地址
- TOKEN：授权Token
- USERNAME：用户名
- PASSWORD：密码

注：配置TOKEN和配置用户名密码二选一

#### 示例
![输入图片说明](demo.png)