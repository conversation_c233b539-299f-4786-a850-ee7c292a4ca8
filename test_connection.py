#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的连接测试脚本
"""

import requests
import json

def test_zentao_connection():
    """测试禅道连接"""
    print("🔍 测试禅道服务器连接...")
    
    # 测试基础连接
    base_urls = [
        "http://127.0.0.1/zentao",
        "http://127.0.0.1/zentao/api.php/v1",
        "http://localhost/zentao",
        "http://localhost/zentao/api.php/v1"
    ]
    
    for url in base_urls:
        try:
            print(f"  测试: {url}")
            response = requests.get(url, timeout=5)
            print(f"    ✅ 连接成功 (状态码: {response.status_code})")
            if response.status_code == 200:
                print(f"    响应长度: {len(response.text)} 字符")
                return url
        except requests.exceptions.RequestException as e:
            print(f"    ❌ 连接失败: {e}")
    
    return None

def test_api_token():
    """测试API Token获取"""
    print("\n🔍 测试API Token获取...")
    
    base_url = "http://127.0.0.1/zentao/api.php/v1"
    token_url = f"{base_url}/tokens"
    
    # 测试用户名密码
    test_credentials = [
        {"account": "admin", "password": "123456"},
        {"account": "sungengyong", "password": "123456"},
        {"account": "admin", "password": "admin"},
        {"account": "sungengyong", "password": "sungengyong"}
    ]
    
    for cred in test_credentials:
        try:
            print(f"  测试用户: {cred['account']}")
            response = requests.post(token_url, json=cred, timeout=10)
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if 'token' in result:
                    print(f"    ✅ Token获取成功: {result['token'][:20]}...")
                    return result['token'], cred
                else:
                    print(f"    ❌ 响应中没有token: {result}")
            else:
                print(f"    ❌ 请求失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"    ❌ 请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"    ❌ JSON解析失败: {e}")
    
    return None, None

def test_api_projects(token):
    """测试项目API"""
    print(f"\n🔍 测试项目API...")
    
    base_url = "http://127.0.0.1/zentao/api.php/v1"
    projects_url = f"{base_url}/projects"
    
    headers = {"Token": token}
    
    try:
        response = requests.get(projects_url, headers=headers, timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            projects = response.json()
            print(f"  ✅ 获取到 {len(projects)} 个项目")
            
            # 查找TBM相关项目
            tbm_projects = []
            for project in projects:
                project_name = project.get('name', '')
                if 'TBM' in project_name or 'tbm' in project_name.lower() or '智能建造' in project_name:
                    tbm_projects.append(project)
                    print(f"    🎯 找到相关项目: {project_name} (ID: {project.get('id')})")
            
            if not tbm_projects:
                print("  ⚠️  未找到TBM或智能建造相关项目")
                print("  📋 所有项目:")
                for i, project in enumerate(projects[:10]):  # 只显示前10个
                    print(f"    {i+1}. {project.get('name')} (ID: {project.get('id')})")
            
            return projects, tbm_projects
        else:
            print(f"  ❌ 请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求异常: {e}")
    
    return [], []

def main():
    """主函数"""
    print("========================================")
    print("禅道连接诊断测试")
    print("========================================")
    
    # 测试基础连接
    working_url = test_zentao_connection()
    if not working_url:
        print("\n❌ 无法连接到禅道服务器，请检查:")
        print("  1. 禅道服务器是否正在运行")
        print("  2. URL是否正确")
        print("  3. 网络连接是否正常")
        return
    
    # 测试Token获取
    token, credentials = test_api_token()
    if not token:
        print("\n❌ 无法获取API Token，请检查:")
        print("  1. 用户名密码是否正确")
        print("  2. 用户是否有API访问权限")
        print("  3. 禅道API是否启用")
        return
    
    print(f"\n✅ 认证成功，用户: {credentials['account']}")
    
    # 测试项目API
    projects, tbm_projects = test_api_projects(token)
    
    if tbm_projects:
        print(f"\n🎯 找到 {len(tbm_projects)} 个TBM相关项目:")
        for project in tbm_projects:
            print(f"  - {project.get('name')} (ID: {project.get('id')})")
    
    print(f"\n✅ 诊断完成！")
    
    if token and credentials:
        print(f"\n📝 可用的配置:")
        print(f"BASE_URL=http://127.0.0.1/zentao/api.php/v1")
        print(f"USERNAME={credentials['account']}")
        print(f"PASSWORD={credentials['password']}")

if __name__ == "__main__":
    main()
