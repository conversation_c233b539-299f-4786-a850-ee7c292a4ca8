<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /></head><body><br />
16:17:37 ERROR: the control file module/zentao/control.php not found. in framework/base/router.class.php on line 1528, last called by framework/router.class.php on line 484 through function setControlFile.<br />
 in <strong>/apps/zentao/framework/base/router.class.php</strong> on line <strong>3037</strong> when visiting <strong>zentao</strong><br />
</body></html><br />
<b>Fatal error</b>:  Uncaught EndResponseException in /apps/zentao/framework/base/router.class.php:3492
Stack trace:
#0 /apps/zentao/framework/helper.class.php(323): EndResponseException::create('')
#1 /apps/zentao/framework/base/router.class.php(3148): helper::end()
#2 /apps/zentao/framework/router.class.php(350): baseRouter-&gt;saveError(256, 'ERROR: the cont...', '/apps/zentao/fr...', 3037)
#3 [internal function]: router-&gt;saveError(256, 'ERROR: the cont...', '/apps/zentao/fr...', 3037, Array)
#4 /apps/zentao/framework/base/router.class.php(3037): trigger_error('ERROR: the cont...', 256)
#5 /apps/zentao/framework/base/router.class.php(1528): baseRouter-&gt;triggerError('the control fil...', '/apps/zentao/fr...', 484, true)
#6 /apps/zentao/framework/router.class.php(484): baseRouter-&gt;setControlFile(true)
#7 /apps/zentao/framework/base/router.class.php(2270): router-&gt;setControlFile()
#8 /apps/zentao/framework/base/router.class.php(1327): baseRouter-&gt;setRouteByPathInfo()
#9 /apps/zentao/www/index.php(72): baseRouter-&gt;parseRequest()
#10  in <b>/apps/zentao/framework/base/router.class.php</b> on line <b>3492</b><br />
<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /></head><body><br />
16:17:37 Uncaught EndResponseException in /apps/zentao/framework/base/router.class.php:3492<br />
Stack trace:<br />
#0 /apps/zentao/framework/helper.class.php(323): EndResponseException::create('')<br />
#1 /apps/zentao/framework/base/router.class.php(3148): helper::end()<br />
#2 /apps/zentao/framework/router.class.php(350): baseRouter->saveError(256, 'ERROR: the cont...', '/apps/zentao/fr...', 3037)<br />
#3 [internal function]: router->saveError(256, 'ERROR: the cont...', '/apps/zentao/fr...', 3037, Array)<br />
#4 /apps/zentao/framework/base/router.class.php(3037): trigger_error('ERROR: the cont...', 256)<br />
#5 /apps/zentao/framework/base/router.class.php(1528): baseRouter->triggerError('the control fil...', '/apps/zentao/fr...', 484, true)<br />
#6 /apps/zentao/framework/router.class.php(484): baseRouter->setControlFile(true)<br />
#7 /apps/zentao/framework/base/router.class.php(2270): router->setControlFile()<br />
#8 /apps/zentao/framework/base/router.class.php(1327): baseRouter->setRouteByPathInfo()<br />
#9 /apps/zentao/www/index.php(72): baseRouter->parseRequest()<br />
#10  in <strong>/apps/zentao/framework/base/router.class.php</strong> on line <strong>3492</strong> when visiting <strong>zentao</strong><br />
</body></html><br />
<b>Fatal error</b>:  Uncaught EndResponseException in /apps/zentao/framework/base/router.class.php:3492
Stack trace:
#0 /apps/zentao/framework/helper.class.php(323): EndResponseException::create('')
#1 /apps/zentao/framework/base/router.class.php(3148): helper::end()
#2 /apps/zentao/framework/router.class.php(350): baseRouter-&gt;saveError(1, 'Uncaught EndRes...', '/apps/zentao/fr...', 3492)
#3 /apps/zentao/framework/base/router.class.php(3003): router-&gt;saveError(1, 'Uncaught EndRes...', '/apps/zentao/fr...', 3492)
#4 [internal function]: baseRouter-&gt;shutdown()
#5 {main}
  thrown in <b>/apps/zentao/framework/base/router.class.php</b> on line <b>3492</b><br />
