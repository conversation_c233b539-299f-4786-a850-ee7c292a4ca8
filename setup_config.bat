@echo off
chcp 65001 >nul
echo ========================================
echo 禅道MCP服务器配置向导
echo ========================================
echo.

REM 检查是否已存在配置文件
if exist "config.env" (
    echo ⚠️  发现已存在的配置文件 config.env
    set /p "overwrite=是否覆盖现有配置？(y/N): "
    if /i not "%overwrite%"=="y" (
        echo 🚫 取消配置
        pause
        exit /b 0
    )
)

echo 📝 开始配置禅道MCP服务器...
echo.

REM 获取基础URL
set /p "base_url=请输入禅道API地址 (默认: http://127.0.0.1/zentao/api.php/v1): "
if "%base_url%"=="" set "base_url=http://127.0.0.1/zentao/api.php/v1"

echo.
echo 🔐 选择认证方式：
echo 1. 用户名密码 (推荐)
echo 2. TOKEN
set /p "auth_type=请选择 (1/2): "

if "%auth_type%"=="2" (
    set /p "token=请输入TOKEN: "
    set "username="
    set "password="
) else (
    set /p "username=请输入用户名 (默认: admin): "
    if "%username%"=="" set "username=admin"
    set /p "password=请输入密码: "
    set "token="
)

echo.
echo 📊 选择日志级别：
echo 1. ERROR (默认，仅显示错误)
echo 2. WARNING (显示警告和错误)
echo 3. INFO (显示信息、警告和错误)
echo 4. DEBUG (显示所有日志)
set /p "log_choice=请选择 (1-4，默认1): "

if "%log_choice%"=="2" (
    set "log_level=WARNING"
) else if "%log_choice%"=="3" (
    set "log_level=INFO"
) else if "%log_choice%"=="4" (
    set "log_level=DEBUG"
) else (
    set "log_level=ERROR"
)

REM 生成配置文件
echo # 禅道MCP服务器配置文件 > config.env
echo # 生成时间：%date% %time% >> config.env
echo. >> config.env
echo BASE_URL=%base_url% >> config.env
echo. >> config.env

if not "%username%"=="" (
    echo USERNAME=%username% >> config.env
    echo PASSWORD=%password% >> config.env
)

if not "%token%"=="" (
    echo TOKEN=%token% >> config.env
)

echo. >> config.env
echo LOG_LEVEL=%log_level% >> config.env

echo ✅ 配置文件已生成：config.env
echo.
echo 📋 配置摘要：
echo   - 基础URL：%base_url%
if not "%username%"=="" (
    echo   - 用户名：%username%
    echo   - 密码：***
)
if not "%token%"=="" (
    echo   - TOKEN：%token%
)
echo   - 日志级别：%log_level%
echo.
echo 🚀 现在可以运行 start_mcp_server.bat 启动服务器
pause
