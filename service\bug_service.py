"""
BUG相关操作
"""

from config.config import BASE_URL
from init import HEADER
from utils.req_util import req, Method

BUG_URL = BASE_URL + '/bugs'
PRODUCT_BUG_URL = BASE_URL + '/products/{}/bugs'
OPERATE_BUG_URL = BASE_URL + '/bugs/{}'


def list_all(product_id):
    return req(Method.GET, PRODUCT_BUG_URL.format(product_id) + "?limit=1000", headers=HEADER)


def create(title, severity, pri, bug_type, product_id):
    data = {
        "title": title,
        "severity": severity,
        "pri": pri,
        "type": bug_type
    }
    return req(Method.POST, PRODUCT_BUG_URL.format(product_id), headers=HEADER, body=data)


def get(bug_id):
    return req(Method.GET, OPERATE_BUG_URL.format(bug_id), headers=HEADER)


def delete(bug_id):
    return req(Method.DELETE, OPERATE_BUG_URL.format(bug_id), headers=HEADER)


def update(bug_id, key, value):
    data = dict()
    data[key] = value
    return req(Method.PUT, OPERATE_BUG_URL.format(bug_id), headers=HEADER, body=data)
